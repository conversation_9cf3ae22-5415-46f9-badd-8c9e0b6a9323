import express from "express"
import { query } from "../db.js"

const router = express.Router()

router.post("/login", (req, res) => {
  const { username, password } = req.body
  const sql = "SELECT * FROM admins WHERE username = ? AND password = ?"

  query(sql, [username, password], (err, results) => {
    if (err) return res.status(500).json({ error: err.message })
    if (results.length === 0) return res.status(401).json({ error: "Invalid credentials" })

    const user = results[0]
    res.json({ message: "Login successful", user: { id: user.id, username: user.username, role: user.role } })
  })
})

router.get("/admin-by-role/:role", (req, res) => {
  const { role } = req.params
  const sql = "SELECT id, username, role FROM admins WHERE role = ?"

  query(sql, [role], (err, results) => {
    if (err) return res.status(500).json({ error: err.message })
    if (results.length === 0) return res.status(404).json({ error: "Admin not found for this role" })

    res.json(results[0])
  })
})

export default router
