import { useDroppable } from "@dnd-kit/core"

const DroppableNode = ({ id, children, className = "" }) => {
  const { isOver, setNodeRef } = useDroppable({
    id,
  })

  return (
    <div
      ref={setNodeRef}
      className={`${className} ${
        isOver ? "bg-blue-100 border-blue-500 border-2" : "bg-white border-gray-200"
      } border rounded-md p-2 min-h-[40px] transition-colors`}
      style={{
        outline: isOver ? "2px dashed #3b82f6" : "none",
        outlineOffset: "2px",
      }}
    >
      {children}
    </div>
  )
}

export default DroppableNode
