import { BrowserRouter as Router, Routes, Route } from "react-router-dom"
import Login from "./pages/Login"
import Dashboard from "./pages/Dashboard"
import SuperAdminDashboard from "./pages/SuperAdminDashboard"
import AddAccount from "./pages/AddAccount"
import ViewAccounts from "./pages/ViewAccounts"
import AccountDetails from "./pages/AccountDetails"
import AddProfile from "./pages/AddProfile"
import ProfileDetails from "./pages/ProfileDetails"
import FishboneDiagram from "./pages/FishboneDiagram"
import AdminManagement from "./pages/AdminManagement"
import ProtectedRoute from "./components/ProtectedRoute"

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<Login />} />
        <Route path="/admin/superadmin" element={<ProtectedRoute><SuperAdminDashboard /></ProtectedRoute>} />
        <Route path="/admin/manage-admins" element={<ProtectedRoute><AdminManagement /></ProtectedRoute>} />
        <Route path="/admin/:role" element={<ProtectedRoute><Dashboard /></ProtectedRoute>} />
        <Route path="/add-account" element={<ProtectedRoute><AddAccount /></ProtectedRoute>} /> 
        <Route path="/view-accounts" element={<ProtectedRoute><ViewAccounts /></ProtectedRoute>} />
        <Route path="/admin/:role/add-account" element={<ProtectedRoute><AddAccount /></ProtectedRoute>} /> 
        <Route path="/admin/:role/view-accounts" element={<ProtectedRoute><ViewAccounts /></ProtectedRoute>} /> 
        <Route path="/admin/:role/account/:id" element={<ProtectedRoute><AccountDetails /></ProtectedRoute>} /> 
        <Route path="/admin/:role/account/:id/add-profile" element={<ProtectedRoute><AddProfile /></ProtectedRoute>} /> 
        <Route path="/admin/:role/account/:id/profile/:profileId" element={<ProtectedRoute><ProfileDetails /></ProtectedRoute>} /> 
        <Route path="/admin/:role/account/:id/profile/:profileId/edit" element={<ProtectedRoute><AddProfile /></ProtectedRoute>} /> 
        <Route path="/admin/:role/account/:id/opportunity/:opportunityId" element={<ProtectedRoute><FishboneDiagram /></ProtectedRoute>} /> 
      </Routes>
    </Router>
  )
}

export default App

