import { useState, useEffect } from "react"
import { useNavigate } from "react-router-dom"

const SuperAdminDashboard = () => {
  const navigate = useNavigate()
  const [sectors, setSectors] = useState([
    { id: 1, name: "<PERSON><PERSON><PERSON>", role: "B<PERSON><PERSON>" },
    { id: 2, name: "Defense", role: "Defense" },
    { id: 3, name: "Telco", role: "Telco" },
    { id: 4, name: "Enterprise", role: "Enterprise" },
  ])
  const [username, setUsername] = useState(localStorage.getItem("username") || "")
  const [toast, setToast] = useState({ message: "", type: "", visible: false })

  useEffect(() => {
    const role = localStorage.getItem("role")
    if (role !== "superadmin") {
      navigate("/")
    }
  }, [navigate])

  const showToast = (message, type) => {
    setToast({ message, type, visible: true })
    setTimeout(() => setToast({ message: "", type: "", visible: false }), 3000)
  }

  const handleSectorClick = (sectorRole) => {

    localStorage.setItem("superadmin_id", localStorage.getItem("admin_id"))
    localStorage.setItem("superadmin_username", localStorage.getItem("username"))

    localStorage.setItem("viewing_as_sector", sectorRole)

    navigate(`/admin/${sectorRole}`)
  }

  const handleLogout = () => {
    localStorage.clear()
    
    sessionStorage.clear()
    
    navigate("/", { replace: true })
    
    window.history.pushState(null, null, "/")
    
    window.addEventListener('popstate', function(event) {
      window.history.pushState(null, null, "/")
    })
  }

  return (
    <div
      className="min-h-screen bg-gradient-to-br from-gray-100 to-gray-200 p-8 relative"
      style={{
        backgroundImage: `url(https://img.freepik.com/free-vector/gradient-abstract-wireframe-background_23-2149009903.jpg?semt=ais_hybrid&w=740)`,
        backgroundSize: "cover",
        backgroundPosition: "center",
      }}
    >
      <div
        className="absolute inset-0"
        style={{
          backdropFilter: "blur(10px)",
          zIndex: 0,
        }}
      ></div>

      <div className="max-w-6xl mx-auto relative z-10">
        <div className="flex justify-between items-center mb-12">
          <div>
            <h1 className="text-3xl font-bold text-white">Super Admin Dashboard</h1>
            <p className="text-white mt-1">Welcome back, {username}!</p>
          </div>
          <div className="flex space-x-4">
            <button
              onClick={() => navigate("/admin/manage-admins")}
              className="bg-purple-600 hover:bg-purple-700 text-white font-semibold px-5 py-2.5 rounded-lg shadow-md transition-all duration-300"
            >
              Manage Admins
            </button>
            <button
              onClick={handleLogout}
              className="bg-red-500 hover:bg-red-600 text-white font-semibold px-5 py-2.5 rounded-lg shadow-md transition-all duration-300"
            >
              Logout
            </button>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-8 mb-12 text-center">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">MindConnect</h1>
          <p className="text-xl text-gray-600">Customer-Centric Project Insights. All in One Place.</p>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-8">
          <h2 className="text-2xl font-bold text-gray-800 mb-8">BUs</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {sectors.map((sector) => (
              <div
                key={sector.id}
                onClick={() => handleSectorClick(sector.role)}
                className="border border-gray-300 rounded-lg p-6 text-center cursor-pointer hover:bg-gray-50 hover:border-blue-300 transition-all duration-300"
              >
                <h3 className="text-xl font-semibold text-gray-700">{sector.name}</h3>
              </div>
            ))}
          </div>
        </div>
      </div>

      {toast.visible && (
        <div
          className={`fixed top-4 right-4 px-4 py-2 rounded-lg shadow-lg text-white transform transition-all duration-300 ${
            toast.type === "success" ? "bg-green-500" : "bg-red-500"
          }`}
        >
          {toast.message}
        </div>
      )}
    </div>
  )
}

export default SuperAdminDashboard
