import express from "express"
import { query } from "../db.js"

const router = express.Router()

router.get("/all", (req, res) => {
  const role = req.headers["x-role"]

  if (role !== "superadmin") {
    return res.status(403).json({ error: "Access denied. Only superadmin can view all admins." })
  }

  const sql = "SELECT id, username, role FROM admins"

  query(sql, (err, results) => {
    if (err) {
      console.error("Error fetching admins:", err.message)
      return res.status(500).json({ error: err.message })
    }
    res.json(results)
  })
})

router.put("/update/:id", (req, res) => {
  const { id } = req.params
  const { username, password } = req.body
  const role = req.headers["x-role"]

  if (role !== "superadmin") {
    return res.status(403).json({ error: "Access denied. Only superadmin can update admin credentials." })
  }

  query("SELECT id FROM admins WHERE username = ? AND id != ?", [username, id], (err, results) => {
    if (err) {
      console.error("Error checking username:", err.message)
      return res.status(500).json({ error: err.message })
    }

    if (results.length > 0) {
      return res.status(400).json({ error: "Username already exists" })
    }

    if (password) {
      const sql = "UPDATE admins SET username = ?, password = ? WHERE id = ?"
      query(sql, [username, password, id], (updateErr) => {
        if (updateErr) {
          console.error("Error updating admin:", updateErr.message)
          return res.status(500).json({ error: updateErr.message })
        }
        res.json({ message: "Admin credentials updated successfully" })
      })
    } else {
      const sql = "UPDATE admins SET username = ? WHERE id = ?"
      query(sql, [username, id], (updateErr) => {
        if (updateErr) {
          console.error("Error updating admin:", updateErr.message)
          return res.status(500).json({ error: updateErr.message })
        }
        res.json({ message: "Admin username updated successfully" })
      })
    }
  })
})

router.post("/create", (req, res) => {
  const { username, password, role } = req.body
  const requestRole = req.headers["x-role"]

  if (requestRole !== "superadmin") {
    return res.status(403).json({ error: "Access denied. Only superadmin can create new admins." })
  }

  if (!username || !password || !role) {
    return res.status(400).json({ error: "Username, password, and role are required" })
  }

  const validRoles = ["superadmin", "BFSI", "Defense", "Telco", "Enterprise"]
  if (!validRoles.includes(role)) {
    return res.status(400).json({ error: "Invalid role. Must be one of: " + validRoles.join(", ") })
  }

  query("SELECT id FROM admins WHERE username = ?", [username], (err, results) => {
    if (err) {
      console.error("Error checking username:", err.message)
      return res.status(500).json({ error: err.message })
    }

    if (results.length > 0) {
      return res.status(400).json({ error: "Username already exists" })
    }

    const sql = "INSERT INTO admins (username, password, role) VALUES (?, ?, ?)"
    query(sql, [username, password, role], (insertErr, result) => {
      if (insertErr) {
        console.error("Error creating admin:", insertErr.message)
        return res.status(500).json({ error: insertErr.message })
      }

      console.log("✅ New admin created successfully:", { id: result.insertId, username, role })
      res.status(201).json({
        message: "Admin created successfully",
        admin: { id: result.insertId, username, role },
      })
    })
  })
})

router.delete("/delete/:id", (req, res) => {
  const { id } = req.params
  const requestRole = req.headers["x-role"]
  const requesterId = req.headers["x-id"]

  if (requestRole !== "superadmin") {
    return res.status(403).json({ error: "Access denied. Only superadmin can delete admins." })
  }

  if (Number.parseInt(id) === Number.parseInt(requesterId)) {
    return res.status(400).json({ error: "Cannot delete your own account" })
  }

  query("SELECT id, role FROM admins WHERE id = ?", [id], (err, results) => {
    if (err) {
      console.error("Error checking admin:", err.message)
      return res.status(500).json({ error: err.message })
    }

    if (results.length === 0) {
      return res.status(404).json({ error: "Admin not found" })
    }

    query("DELETE FROM admins WHERE id = ?", [id], (deleteErr) => {
      if (deleteErr) {
        console.error("Error deleting admin:", deleteErr.message)
        return res.status(500).json({ error: deleteErr.message })
      }

      console.log("✅ Admin deleted successfully:", id)
      res.json({ message: "Admin deleted successfully" })
    })
  })
})

export default router