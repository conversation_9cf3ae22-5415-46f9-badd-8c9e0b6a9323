import express, { json } from "express"
import cors from "cors"
import dotenv from "dotenv"
dotenv.config()

import { query } from "./db.js"
import { initializeDatabase } from "./Database/initDatabase.js"
import authRoutes from "./Routes/auth.js"
import accountRoutes from "./Routes/account.js"
import opportunityRoutes from "./Routes/opportunity.js"
import profileRoutes from "./Routes/profile.js"
import fishboneRoutes from "./Routes/fishbone.js"
import adminRoutes from "./Routes/admin.js"

const app = express()
app.use(cors())
app.use(json())

app.use((req, res, next) => {
  console.log(`${req.method} ${req.url}`)
  next()
})

const startServer = async () => {
  try {
    await initializeDatabase()

    app.use("/api/auth", authRoutes)
    app.use("/uploads", express.static("Backend/uploads"))
    app.use("/api/accounts", accountRoutes)
    app.use("/api/opportunities", opportunityRoutes)
    app.use("/api/profiles", profileRoutes)
    app.use("/api/fishbone", fishboneRoutes)
    app.use("/api/admins", adminRoutes)

    app.get("/", (req, res) => {
      res.send("🎉 Backend is running and connected to MySQL")
    })

    app.get("/test-db", (req, res) => {
      query("SELECT 1 + 1 AS result", (err, results) => {
        if (err) {
          console.error("Database query error:", err)
          return res.status(500).json({ error: "Error querying the database", details: err.message })
        }
        res.json({ result: results[0].result, message: "Database connection successful" })
      })
    })

    app.use((err, req, res, next) => {
      console.error("Server error:", err)
      res.status(500).json({ error: "Internal server error", message: err.message })
    })

    const PORT = process.env.PORT || 5000
    app.listen(PORT, () => {
      console.log(`🚀 Server is running on http://localhost:${PORT}`)
      console.log(`Environment: ${process.env.NODE_ENV || "development"}`)
      console.log(`Database: ${process.env.DB_NAME || "mnconnect"} on ${process.env.DB_HOST || "localhost"}`)
      console.log("🎯 Application is ready to use!")
    })
  } catch (error) {
    console.error("❌ Failed to start server:", error.message)
    process.exit(1)
  }
}

startServer()
