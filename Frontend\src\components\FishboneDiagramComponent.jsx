import { useState, useEffect } from "react"
import { DndContext, DragOverlay, MouseSensor, TouchSensor, useSensor, useSensors, pointerWithin } from "@dnd-kit/core"
import DroppableNode from "./DroppableNode"
import ProfileNodeContent from "./ProfileNodeContent"

const FishboneDiagram = ({
  profiles,
  departments = [],
  setDepartments,
  nodePlacements = [],
  setNodePlacements,
  onSave,
}) => {
  const [activeId, setActiveId] = useState(null)
  const [activeProfile, setActiveProfile] = useState(null)
  const [newDepartment, setNewDepartment] = useState("")

  useEffect(() => {
    console.log("FishboneDiagram rendered with:", {
      profilesCount: profiles?.length || 0,
      departments: departments || [],
      nodePlacements: nodePlacements || [],
    })
  }, [profiles, departments, nodePlacements])

  const mouseSensor = useSensor(MouseSensor, {
    activationConstraint: {
      distance: 5,
    },
  })

  const touchSensor = useSensor(TouchSensor, {
    activationConstraint: {
      delay: 100,
      tolerance: 5,
    },
  })

  const sensors = useSensors(mouseSensor, touchSensor)

  const handleDragStart = (event) => {
    const { active } = event
    console.log("Drag start:", active)
    setActiveId(active.id)

    if (active.data.current?.type === "profile") {
      setActiveProfile(active.data.current.profile)
    }
  }

  const handleDragEnd = (event) => {
    const { active, over } = event
    console.log("Drag end:", { active, over })

    if (over && active.data.current?.type === "profile") {
      const profileId = active.data.current.profile.id
      const nodeId = over.id

      if (nodeId.startsWith("department-")) {
        const departmentIndex = Number.parseInt(nodeId.split("-")[1])
        console.log(`Dropping profile ${profileId} onto department index ${departmentIndex}`)

        const updatedPlacements = [...(nodePlacements || [])]

        const existingPlacementIndex = updatedPlacements.findIndex((p) => p.profile_id === profileId)

        if (existingPlacementIndex !== -1) {
          updatedPlacements[existingPlacementIndex].department_index = departmentIndex
          console.log("Updated existing placement")
        } else {
          updatedPlacements.push({
            profile_id: profileId,
            department_index: departmentIndex,
          })
          console.log("Added new placement")
        }

        setNodePlacements(updatedPlacements)

        console.log("Saving with:", { departments, updatedPlacements })
        onSave(departments || [], updatedPlacements)
      }
    }

    setActiveId(null)
    setActiveProfile(null)
  }

  const handleDragOver = (event) => {
    console.log("Drag over:", event)
  }

  const handleAddDepartment = () => {
    if (newDepartment.trim() !== "") {
      const updatedDepartments = [...(departments || []), newDepartment.trim()]
      console.log("Adding department:", newDepartment.trim(), "New departments:", updatedDepartments)
      setDepartments(updatedDepartments)
      setNewDepartment("")
      onSave(updatedDepartments, nodePlacements || [])
    }
  }

  const handleRemoveDepartment = (index) => {
    console.log("Removing department at index:", index)
    const updatedDepartments = [...(departments || [])]
    updatedDepartments.splice(index, 1)

    const updatedPlacements = (nodePlacements || []).filter((p) => p.department_index !== index)

    updatedPlacements.forEach((p) => {
      if (p.department_index > index) {
        p.department_index -= 1
      }
    })

    setDepartments(updatedDepartments)
    setNodePlacements(updatedPlacements)
    onSave(updatedDepartments, updatedPlacements)
  }

  const getProfilesForDepartment = (departmentIndex) => {
    if (!nodePlacements || !profiles) return []

    const placementsForDepartment = nodePlacements.filter((p) => p.department_index === departmentIndex)
    return placementsForDepartment
      .map((p) => {
        return profiles.find((profile) => profile.id === p.profile_id)
      })
      .filter(Boolean)
  }

  const handleRemoveProfileFromDepartment = (profileId) => {
    console.log("Removing profile from department:", profileId)
    const updatedPlacements = (nodePlacements || []).filter((p) => p.profile_id !== profileId)
    setNodePlacements(updatedPlacements)
    onSave(departments || [], updatedPlacements)
  }

  const departmentsArray = Array.isArray(departments) ? departments : []

  // Calculate positions for fishbone structure
  const getFishboneLayout = () => {
    const centerX = 450
    const centerY = 300
    const spineLength = 700
    const branchLength = 140
    const branchAngle = 45 // degrees

    return departmentsArray.map((department, index) => {
      const isEven = index % 2 === 0
      const branchIndex = Math.floor(index / 2)
      const totalBranches = Math.ceil(departmentsArray.length / 2)

      // Position along the spine with better spacing
      const spineProgress = totalBranches > 1 ? (branchIndex + 0.5) / totalBranches : 0.5
      const spineX = centerX - spineLength/2 + spineProgress * spineLength

      // Branch direction (up or down)
      const direction = isEven ? -1 : 1
      const angleRad = (branchAngle * Math.PI) / 180

      // Branch end position
      const branchX = spineX + Math.cos(angleRad) * branchLength
      const branchY = centerY + direction * Math.sin(angleRad) * branchLength

      return {
        department,
        index,
        spineX,
        spineY: centerY,
        branchX,
        branchY,
        direction,
        angle: direction * branchAngle,
        branchLength
      }
    })
  }

  const fishboneLayout = getFishboneLayout()

  return (
    <div className="w-full">
      <div className="mb-6 flex">
        <input
          type="text"
          value={newDepartment}
          onChange={(e) => setNewDepartment(e.target.value)}
          placeholder="Add new department"
          className="flex-1 border border-gray-300 rounded-l-lg p-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        <button
          onClick={handleAddDepartment}
          className="bg-blue-500 text-white px-4 py-2 rounded-r-lg hover:bg-blue-600 transition-colors"
        >
          Add Department
        </button>
      </div>

      <DndContext
        sensors={sensors}
        collisionDetection={pointerWithin}
        onDragStart={handleDragStart}
        onDragOver={handleDragOver}
        onDragEnd={handleDragEnd}
      >
        <div className="relative w-full overflow-x-auto pb-6">
          <div className="min-w-[1000px] min-h-[700px] relative">
            {/* Main Spine */}
            <div
              className="absolute bg-gradient-to-r from-gray-500 to-gray-600 shadow-sm z-0"
              style={{
                left: '100px',
                top: '299px',
                width: '700px',
                height: '3px',
              }}
            />

            {/* Head of the fish (Problem/Opportunity) */}
            <div
              className="absolute bg-gradient-to-r from-indigo-600 to-indigo-700 text-white rounded-full px-8 py-4 text-lg font-bold shadow-xl z-10 border-2 border-white"
              style={{
                left: '820px',
                top: '280px',
                transform: 'translateY(-50%)'
              }}
            >
              Opportunity Map
            </div>

            {/* Department Branches */}
            {fishboneLayout.map((layout) => (
              <div key={layout.index}>
                {/* Branch Line from spine to department */}
                <div
                  className="absolute bg-gradient-to-r from-gray-500 to-gray-400 shadow-sm z-5"
                  style={{
                    left: `${layout.spineX}px`,
                    top: `${layout.spineY}px`,
                    width: `${layout.branchLength}px`,
                    height: '3px',
                    transformOrigin: '0 50%',
                    transform: `rotate(${layout.angle}deg)`,
                  }}
                />

                {/* Department Node */}
                <div
                  className="absolute z-10"
                  style={{
                    left: `${layout.branchX}px`,
                    top: `${layout.branchY}px`,
                    transform: 'translate(-50%, -50%)'
                  }}
                >
                  <div className="flex items-center">
                    <DroppableNode
                      id={`department-${layout.index}`}
                      className="bg-gradient-to-r from-blue-50 to-indigo-50 border-2 border-indigo-400 rounded-lg px-5 py-3 font-semibold shadow-lg text-sm hover:border-indigo-600 hover:shadow-xl transition-all duration-200 text-indigo-800"
                    >
                      {layout.department}
                    </DroppableNode>
                    <button
                      onClick={() => handleRemoveDepartment(layout.index)}
                      className="ml-2 text-red-500 hover:text-red-700 bg-white rounded-full p-1.5 shadow-md hover:shadow-lg transition-all duration-200"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-4 w-4"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </button>
                  </div>

                  {/* Profiles as diagonal sub-branches */}
                  <div className="absolute">
                    {getProfilesForDepartment(layout.index).map((profile, profileIndex) => {
                      // Calculate diagonal sub-branch positions
                      const subBranchLength = 80
                      const subBranchAngle = layout.direction > 0 ? -30 : 30 // Diagonal angle for sub-branches
                      const subAngleRad = (subBranchAngle * Math.PI) / 180

                      // Offset along the department branch for multiple profiles
                      const branchOffset = (profileIndex + 1) * 25
                      const offsetX = Math.cos((layout.angle * Math.PI) / 180) * branchOffset
                      const offsetY = Math.sin((layout.angle * Math.PI) / 180) * branchOffset

                      // Starting point on the department branch
                      const startX = offsetX
                      const startY = offsetY

                      // End point of the sub-branch
                      const endX = startX + Math.cos(subAngleRad) * subBranchLength
                      const endY = startY + Math.sin(subAngleRad) * subBranchLength

                      return (
                        <div key={profile.id}>
                          {/* Diagonal sub-branch line */}
                          <div
                            className="absolute bg-gradient-to-r from-gray-400 to-gray-300 z-5"
                            style={{
                              left: `${startX}px`,
                              top: `${startY}px`,
                              width: `${subBranchLength}px`,
                              height: '2px',
                              transformOrigin: '0 50%',
                              transform: `rotate(${subBranchAngle}deg)`,
                            }}
                          />

                          {/* Profile node at the end of sub-branch */}
                          <div
                            className="absolute z-10"
                            style={{
                              left: `${endX}px`,
                              top: `${endY}px`,
                              transform: 'translate(-50%, -50%)'
                            }}
                          >
                            <ProfileNodeContent
                              profile={profile}
                              onRemove={() => handleRemoveProfileFromDepartment(profile.id)}
                            />
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        <DragOverlay>
          {activeId && activeProfile ? (
            <div className="bg-white rounded-lg shadow-lg p-3 border border-blue-300 w-48">
              <div className="flex items-center space-x-3">
                {activeProfile.profile_photo ? (
                  <img
                    src={`http://localhost:5000/uploads/profiles/${activeProfile.profile_photo}`}
                    alt={activeProfile.full_name}
                    className="w-10 h-10 rounded-full object-cover"
                  />
                ) : (
                  <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-6 w-6 text-gray-400"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                      />
                    </svg>
                  </div>
                )}
                <div>
                  <h3 className="font-medium text-sm text-gray-800">{activeProfile.full_name}</h3>
                  <p className="text-xs text-gray-500">{activeProfile.designation}</p>
                </div>
              </div>
            </div>
          ) : null}
        </DragOverlay>
      </DndContext>
    </div>
  )
}

export default FishboneDiagram
