import express from "express"
import {
  addProfile,
  getProfilesByAccount,
  getProfileById,
  deleteProfile,
  updateProfile,
  upload,
} from "../Controllers/profileController.js"

const router = express.Router()

router.use((req, res, next) => {
  console.log(`Profile Route: ${req.method} ${req.originalUrl}`)
  next()
})

router.post("/add", upload.single("profile_photo"), addProfile)
router.put("/update/:id", upload.single("profile_photo"), updateProfile)
router.get("/account/:account_id", getProfilesByAccount)
router.get("/:id", getProfileById)
router.delete("/delete/:id", deleteProfile)

export default router
