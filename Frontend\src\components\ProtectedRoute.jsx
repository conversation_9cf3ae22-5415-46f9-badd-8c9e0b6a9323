import { useEffect } from "react"
import { useNavigate } from "react-router-dom"

const ProtectedRoute = ({ children }) => {
  const navigate = useNavigate()

  useEffect(() => {
    const isAuthenticated = () => {
      const username = localStorage.getItem("username")
      const adminId = localStorage.getItem("admin_id")
      const role = localStorage.getItem("role")
      
      return username && adminId && role
    }

    if (!isAuthenticated()) {
      localStorage.clear()
      sessionStorage.clear()
      
      navigate("/", { replace: true })
      
      window.history.pushState(null, null, "/")
      
      return
    }

    const handlePopState = (event) => {
      if (!isAuthenticated()) {
        window.history.pushState(null, null, "/")
        navigate("/", { replace: true })
      }
    }

    window.addEventListener('popstate', handlePopState)

    return () => {
      window.removeEventListener('popstate', handlePopState)
    }
  }, [navigate])

  const username = localStorage.getItem("username")
  const adminId = localStorage.getItem("admin_id")
  const role = localStorage.getItem("role")

  if (!username || !adminId || !role) {
    return null
  }

  return children
}

export default ProtectedRoute
