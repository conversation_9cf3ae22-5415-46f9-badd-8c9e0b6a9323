const ProfileNodeContent = ({ profile, onRemove }) => {
  return (
    <div className="bg-gradient-to-r from-white to-gray-50 rounded-lg shadow-lg p-3 border-2 border-blue-200 hover:border-blue-400 transition-all duration-300 hover:shadow-xl transform hover:scale-105 min-w-[160px]">
      <div className="flex items-center space-x-3">
        {profile.profile_photo ? (
          <img
            src={`http://localhost:5000/uploads/profiles/${profile.profile_photo}`}
            alt={profile.full_name}
            className="w-10 h-10 rounded-full object-cover border-2 border-white shadow-md"
          />
        ) : (
          <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-400 to-purple-500 flex items-center justify-center shadow-md">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 text-white"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
              />
            </svg>
          </div>
        )}
        <div className="flex-1 min-w-0">
          <h3 className="font-semibold text-sm text-gray-800 truncate">{profile.full_name}</h3>
          <p className="text-xs text-gray-600 truncate">{profile.designation}</p>
        </div>
        <button
          onClick={onRemove}
          className="text-red-400 hover:text-red-600 hover:bg-red-50 rounded-full p-1 transition-all duration-200 flex-shrink-0"
          title="Remove from diagram"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
            <path
              fillRule="evenodd"
              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
              clipRule="evenodd"
            />
          </svg>
        </button>
      </div>
    </div>
  )
}

export default ProfileNodeContent
