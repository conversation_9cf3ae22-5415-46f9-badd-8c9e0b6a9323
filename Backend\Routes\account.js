import express from "express"
import {
  addAccount,
  getAccountsByAdmin,
  updateAccount,
  deleteAccount,
  getAccountById,
  upload,
} from "../Controllers/accountController.js"

const router = express.Router()

router.post("/add", upload.single("image"), addAccount)
router.get("/admin/:admin_id", getAccountsByAdmin)
router.get("/:id", getAccountById)
router.put("/update/:id", updateAccount)
router.delete("/delete/:id", deleteAccount)

export default router
