import { useEffect, useState } from "react"
import { useNavigate, use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom"
import axios from "axios"
import SuperAdminHeader from "../components/SuperAdminHeader"

const styles = `
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  @keyframes scaleIn {
    from { transform: scale(0.9); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
  }
  .animate-fadeIn {
    animation: fadeIn 0.3s ease-out;
  }
  .animate-scaleIn {
    animation: scaleIn 0.3s ease-out;
  }
`

if (typeof document !== 'undefined') {
  const styleSheet = document.createElement("style")
  styleSheet.type = "text/css"
  styleSheet.innerText = styles
  document.head.appendChild(styleSheet)
}

const Dashboard = () => {
  const { role } = useParams()
  const navigate = useNavigate()
  const username = localStorage.getItem("username")
  const adminId = localStorage.getItem("admin_id")
  const [accounts, setAccounts] = useState([])
  const [filteredAccounts, setFilteredAccounts] = useState([])
  const [selectedAccountType, setSelectedAccountType] = useState("all")
  const [toast, setToast] = useState({ message: "", type: "", visible: false })
  const [confirmDelete, setConfirmDelete] = useState({ id: null, visible: false })
  const viewingAsSector = localStorage.getItem("viewing_as_sector")

  const accountTypes = [...new Set(accounts.map(acc => acc.type || 'Other').filter(Boolean))]
  
  const formatSectorName = (sectorName) => {
    if (!sectorName) return "Admin"
    const lowerSector = sectorName.toLowerCase()
    switch (lowerSector) {
      case 'bfsi':
        return 'BFSI'
      case 'defense':
        return 'Defense'
      case 'telco':
        return 'Telco'
      case 'enterprise':
        return 'Enterprise'
      default:
        return sectorName.charAt(0).toUpperCase() + sectorName.slice(1)
    }
  }
  
  const filterAccounts = (type) => {
    if (type === "all") {
      setFilteredAccounts(accounts)
    } else {
      setFilteredAccounts(accounts.filter(acc => (acc.type || 'Other') === type))
    }
    setSelectedAccountType(type)
  }

  const updateAccountsAndFilter = (newAccounts) => {
    setAccounts(newAccounts)
    if (selectedAccountType === "all") {
      setFilteredAccounts(newAccounts)
    } else {
      setFilteredAccounts(newAccounts.filter(acc => (acc.type || 'Other') === selectedAccountType))
    }
  }

  const showToast = (message, type) => {
    setToast({ message, type, visible: true })
    setTimeout(() => setToast({ message: "", type: "", visible: false }), 3000)
  }

  const showConfirmDelete = (id) => {
    setConfirmDelete({ id, visible: true })
  }

  const hideConfirmDelete = () => {
    setConfirmDelete({ id: null, visible: false })
  }

  const handleDelete = async (id) => {
    hideConfirmDelete()
    try {
      await axios.delete(`http://localhost:5000/api/accounts/delete/${id}`)
      fetchAccounts()
      showToast("Account deleted successfully!", "success")
    } catch (error) {
      console.error("Error deleting account:", error)
      showToast("Error deleting account", "error")
    }
  }

  const fetchAccounts = async () => {
    try {
      const sectorRole = viewingAsSector || role

      let res
      if (viewingAsSector) {
        const adminRes = await axios.get(`http://localhost:5000/api/auth/admin-by-role/${sectorRole}`)
        if (adminRes.data && adminRes.data.id) {
          res = await axios.get(`http://localhost:5000/api/accounts/admin/${adminRes.data.id}`)
        } else {
          showToast(`No admin found for ${sectorRole} sector`, "error")
          return
        }
      } else {
        res = await axios.get(`http://localhost:5000/api/accounts/admin/${adminId}`)
      }

      updateAccountsAndFilter(res.data)
    } catch (error) {
      console.error("Error fetching accounts:", error)
      showToast("Error fetching accounts", "error")
    }
  }

  useEffect(() => {
    const storedRole = localStorage.getItem("role")
    if (!viewingAsSector && storedRole !== role) {
      logout()
      return
    }

    fetchAccounts()
  }, [role, viewingAsSector])

  const logout = () => {
    localStorage.clear()
    
    sessionStorage.clear()
    
    navigate("/", { replace: true })
    
    window.history.pushState(null, null, "/")
    
    window.addEventListener('popstate', function(event) {
      window.history.pushState(null, null, "/")
    })
  }

  return (
    <>
      <SuperAdminHeader />
      <div 
        className="min-h-screen p-4 md:p-8 relative overflow-hidden bg-gradient-to-br from-slate-900 via-blue-900 to-purple-900"
      >
        <div className="absolute inset-0 bg-black/10"></div>

        <div className="max-w-7xl mx-auto relative z-10">
          <div className="flex flex-col lg:flex-row justify-between items-center mb-8 space-y-4 lg:space-y-0">
            <div className="text-center lg:text-left">
              <div className="flex items-center justify-center lg:justify-start mb-3">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mr-3 shadow-lg">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <div>
                  <h1 className="text-3xl lg:text-4xl font-bold text-white">
                    {formatSectorName(viewingAsSector || role)} Dashboard
                  </h1>
                </div>
              </div>
              <p className="text-lg text-white font-medium">Welcome back, <span className="text-blue-300 font-semibold">{username || "Admin"}</span>! 👋</p>
              <p className="text-white/80 text-sm mt-2">Manage your accounts and explore new opportunities</p>
            </div>
            
            <div className="flex items-center space-x-3">
              <Link
                to={`/admin/${viewingAsSector || role}/add-account`}
                className="bg-gradient-to-r from-blue-600 via-blue-700 to-indigo-700 hover:from-blue-700 hover:via-blue-800 hover:to-indigo-800 text-white font-semibold px-5 py-3 rounded-xl shadow-lg shadow-blue-500/25 transition-all duration-300 flex items-center transform hover:scale-105 hover:shadow-xl hover:shadow-blue-500/40 group"
              >
                <div className="bg-white/20 p-1.5 rounded-lg mr-2 group-hover:bg-white/30 transition-colors duration-300">
                  <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                </div>
                Add Account
              </Link>
              <button
                onClick={logout}
                className="bg-gradient-to-r from-slate-600 to-slate-700 hover:from-slate-700 hover:to-slate-800 text-white font-semibold px-5 py-3 rounded-xl shadow-lg transition-all duration-300 transform hover:scale-105 flex items-center group"
              >
                <div className="bg-white/20 p-1.5 rounded-lg mr-2 group-hover:bg-white/30 transition-colors duration-300">
                  <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                  </svg>
                </div>
                Logout
              </button>
            </div>
          </div>

          <div className="mb-8">
            <div className="bg-white backdrop-blur-xl rounded-3xl shadow-2xl shadow-white/20 border border-white/30 p-8">
              <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-8 space-y-6 lg:space-y-0">
                <div className="space-y-2">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                      <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                      </svg>
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold text-gray-800">Account Overview</h2>
                      <p className="text-gray-600 text-sm">Insights into your account portfolio performance</p>
                    </div>
                  </div>
                </div>
                
                <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-3 sm:space-y-0 sm:space-x-4">
                  <div className="flex items-center space-x-2">
                    <div className="w-6 h-6 bg-indigo-100 rounded-lg flex items-center justify-center">
                      <svg className="w-3 h-3 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                      </svg>
                    </div>
                    <label className="text-sm font-semibold text-gray-700">Filter Accounts</label>
                  </div>
                  <div className="relative">
                    <select
                      value={selectedAccountType}
                      onChange={(e) => filterAccounts(e.target.value)}
                      className="appearance-none bg-white border-2 border-gray-200 hover:border-blue-300 focus:border-blue-500 rounded-xl px-4 py-3 pr-10 text-sm font-semibold text-gray-700 focus:ring-2 focus:ring-blue-200 focus:outline-none transition-all duration-300 shadow-lg hover:shadow-xl cursor-pointer min-w-[160px]"
                    >
                      <option value="all"> All Types ({accounts.length})</option>
                      {accountTypes.map(type => (
                        <option key={type} value={type}>
                           {type} ({accounts.filter(acc => (acc.type || 'Other') === type).length})
                        </option>
                      ))}
                    </select>
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                      <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </div>
                  {selectedAccountType !== "all" && (
                    <button
                      onClick={() => filterAccounts("all")}
                      className="text-xs bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-gray-800 px-3 py-2 rounded-lg transition-all duration-200 flex items-center space-x-1 font-medium"
                    >
                      <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                      <span>Clear Filter</span>
                    </button>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="group bg-white hover:bg-gray-50 rounded-2xl p-6 border-2 border-gray-200 hover:border-gray-300 transition-all duration-300 cursor-pointer transform hover:scale-[1.02] hover:shadow-xl">
                  <div className="flex items-start justify-between mb-4">
                    <div className="space-y-1">
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <p className="text-xs font-bold text-gray-700 uppercase tracking-wide">Total Portfolio</p>
                      </div>
                      <p className="text-3xl font-black text-gray-800 group-hover:text-gray-900 transition-colors duration-300">
                        {accounts.length}
                      </p>
                      <p className="text-xs text-gray-600 font-medium">
                        {accounts.length === 1 ? 'Account' : 'Accounts'} Managed
                      </p>
                    </div>
                    <div className="w-14 h-14 bg-gray-100 group-hover:bg-gray-200 rounded-2xl flex items-center justify-center transition-all duration-300 group-hover:scale-110">
                      <svg className="w-7 h-7 text-gray-600 group-hover:text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h4M9 7h6m-6 4h6m-6 4h6" />
                      </svg>
                    </div>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-blue-500 h-2 rounded-full transition-all duration-500" style={{width: '100%'}}></div>
                  </div>
                </div>

                <div className="group bg-white hover:bg-gray-50 rounded-2xl p-6 border-2 border-gray-200 hover:border-gray-300 transition-all duration-300 cursor-pointer transform hover:scale-[1.02] hover:shadow-xl">
                  <div className="flex items-start justify-between mb-4">
                    <div className="space-y-1">
                      <div className="flex items-center space-x-2">
                        <div className={`w-2 h-2 rounded-full ${selectedAccountType === "all" ? "bg-green-500" : "bg-amber-500"}`}></div>
                        <p className="text-xs font-bold text-gray-700 uppercase tracking-wide">
                          {selectedAccountType === "all" ? "All Results" : "Filtered View"}
                        </p>
                      </div>
                      <p className="text-3xl font-black text-gray-800 group-hover:text-gray-900 transition-colors duration-300">
                        {filteredAccounts.length}
                      </p>
                      <p className="text-xs text-gray-600 font-medium">
                        {selectedAccountType === "all" 
                          ? "Complete Portfolio"
                          : `${selectedAccountType} Accounts`
                        }
                      </p>
                    </div>
                    <div className="w-14 h-14 bg-gray-100 group-hover:bg-gray-200 rounded-2xl flex items-center justify-center transition-all duration-300 group-hover:scale-110">
                      <svg className="w-7 h-7 text-gray-600 group-hover:text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                      </svg>
                    </div>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full transition-all duration-500 ${
                        selectedAccountType === "all" 
                          ? "bg-green-500"
                          : "bg-amber-500"
                      }`} 
                      style={{width: accounts.length ? `${(filteredAccounts.length / accounts.length) * 100}%` : '0%'}}
                    ></div>
                  </div>
                </div>

                <div className="group bg-white hover:bg-gray-50 rounded-2xl p-6 border-2 border-gray-200 hover:border-gray-300 transition-all duration-300 cursor-pointer transform hover:scale-[1.02] hover:shadow-xl">
                  <div className="flex items-start justify-between mb-4">
                    <div className="space-y-1">
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                        <p className="text-xs font-bold text-gray-700 uppercase tracking-wide">Categories</p>
                      </div>
                      <p className="text-3xl font-black text-gray-800 group-hover:text-gray-900 transition-colors duration-300">
                        {accountTypes.length}
                      </p>
                      <p className="text-xs text-gray-600 font-medium">
                        {accountTypes.length === 1 ? 'Type' : 'Types'} Available
                      </p>
                    </div>
                    <div className="w-14 h-14 bg-gray-100 group-hover:bg-gray-200 rounded-2xl flex items-center justify-center transition-all duration-300 group-hover:scale-110">
                      <svg className="w-7 h-7 text-gray-600 group-hover:text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                      </svg>
                    </div>
                  </div>
                  <div className="space-y-2">
                    {accountTypes.slice(0, 3).map((type, index) => (
                      <div key={type} className="flex items-center justify-between text-xs">
                        <span className="text-gray-600 font-medium">{type}</span>
                        <span className="text-purple-500 font-semibold">
                          {accounts.filter(acc => (acc.type || 'Other') === type).length}
                        </span>
                      </div>
                    ))}
                    {accountTypes.length > 3 && (
                      <p className="text-xs text-gray-500 font-medium">+{accountTypes.length - 3} more...</p>
                    )}
                  </div>
                </div>

                <div className="group bg-white hover:bg-gray-50 rounded-2xl p-6 border-2 border-gray-200 hover:border-gray-300 transition-all duration-300 cursor-pointer transform hover:scale-[1.02] hover:shadow-xl">
                  <div className="flex items-start justify-between mb-4">
                    <div className="space-y-1">
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
                        <p className="text-xs font-bold text-gray-700 uppercase tracking-wide">Recent Activity</p>
                      </div>
                      <p className="text-3xl font-black text-gray-800 group-hover:text-gray-900 transition-colors duration-300">
                        {accounts.filter(acc => {
                          const weekAgo = new Date()
                          weekAgo.setDate(weekAgo.getDate() - 7)
                          return new Date(acc.created_at) >= weekAgo
                        }).length}
                      </p>
                      <p className="text-xs text-gray-600 font-medium">Last 7 Days</p>
                    </div>
                    <div className="w-14 h-14 bg-gray-100 group-hover:bg-gray-200 rounded-2xl flex items-center justify-center transition-all duration-300 group-hover:scale-110">
                      <svg className="w-7 h-7 text-gray-600 group-hover:text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-orange-500 h-2 rounded-full transition-all duration-500"
                        style={{
                          width: accounts.length ? 
                            `${Math.max(10, (accounts.filter(acc => {
                              const weekAgo = new Date()
                              weekAgo.setDate(weekAgo.getDate() - 7)
                              return new Date(acc.created_at) >= weekAgo
                            }).length / accounts.length) * 100)}%` : '0%'
                        }}
                      ></div>
                    </div>
                    <span className="text-xs text-orange-600 font-semibold whitespace-nowrap">
                      {accounts.length ? 
                        `${Math.round((accounts.filter(acc => {
                          const weekAgo = new Date()
                          weekAgo.setDate(weekAgo.getDate() - 7)
                          return new Date(acc.created_at) >= weekAgo
                        }).length / accounts.length) * 100)}%` : '0%'
                      }
                    </span>
                  </div>
                </div>
              </div>

              {accounts.length > 0 && (
                <div className="mt-8 pt-6 border-t border-gray-200">
                  <div className="flex flex-wrap items-center justify-between gap-4">
                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                        <span className="font-medium">
                          {Math.round((filteredAccounts.length / accounts.length) * 100)}% of total shown
                        </span>
                      </div>
                      <div className="w-px h-4 bg-gray-300"></div>
                      <span className="font-medium">
                        Last updated: {new Date().toLocaleDateString('en-US', { 
                          month: 'short', 
                          day: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </span>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      <button 
                        onClick={fetchAccounts}
                        className="flex items-center space-x-2 text-xs bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-gray-800 px-3 py-2 rounded-lg transition-all duration-200 font-medium group"
                      >
                        <svg className="w-3 h-3 group-hover:rotate-180 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                        <span>Refresh</span>
                      </button>
                      
                      <Link
                        to={`/admin/${viewingAsSector || role}/add-account`}
                        className="flex items-center space-x-2 text-xs bg-blue-100 hover:bg-blue-200 text-blue-600 hover:text-blue-800 px-3 py-2 rounded-lg transition-all duration-200 font-medium group"
                      >
                        <svg className="w-3 h-3 group-hover:rotate-90 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                        </svg>
                        <span>Add New</span>
                      </Link>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredAccounts.length === 0 ? (
              <div className="col-span-full text-center py-16">
                <div className="bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl shadow-blue-200/50 border border-white/20 p-12 max-w-md mx-auto relative overflow-hidden">
                  <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-blue-400/10 to-indigo-400/10 rounded-full blur-2xl"></div>
                  <div className="absolute bottom-0 left-0 w-16 h-16 bg-gradient-to-br from-purple-400/10 to-pink-400/10 rounded-full blur-xl"></div>
                  
                  <div className="relative z-10">
                    <div className="w-16 h-16 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                      <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h4M9 7h6m-6 4h6m-6 4h6"
                        />
                      </svg>
                    </div>
                    <h3 className="text-xl font-bold text-white mb-2">
                      {selectedAccountType === "all" ? "No accounts yet" : `No ${selectedAccountType} accounts found`}
                    </h3>
                    <p className="text-white/80 text-sm mb-6 leading-relaxed">
                      {selectedAccountType === "all" 
                        ? "Ready to get started? Create your first account to begin managing your business relationships."
                        : `Try selecting a different account type or create a new ${selectedAccountType} account.`
                      }
                    </p>
                    {selectedAccountType !== "all" ? (
                      <div className="space-y-3">
                        <button
                          onClick={() => filterAccounts("all")}
                          className="inline-flex items-center bg-gradient-to-r from-slate-600 to-slate-700 hover:from-slate-700 hover:to-slate-800 text-white font-semibold px-6 py-3 rounded-xl shadow-lg transition-all duration-300 transform hover:scale-105 group"
                        >
                          <svg className="w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                          </svg>
                          Show All Accounts
                        </button>
                      </div>
                    ) : (
                      <Link
                        to={`/admin/${viewingAsSector || role}/add-account`}
                        className="inline-flex items-center bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold px-6 py-3 rounded-xl shadow-lg shadow-blue-500/25 transition-all duration-300 transform hover:scale-105 hover:shadow-xl hover:shadow-blue-500/40 group"
                      >
                        <svg className="w-4 h-4 mr-2 group-hover:rotate-90 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                        </svg>
                        Create Your First Account
                      </Link>
                    )}
                  </div>
                </div>
              </div>
            ) : (
              filteredAccounts.map((acc) => (
                <div
                  key={acc.id}
                  className="group bg-white rounded-2xl shadow-xl shadow-white/10 border border-white/20 overflow-hidden transform transition-all duration-300 hover:scale-[1.02] hover:shadow-2xl hover:shadow-white/20 hover:border-white/30 relative backdrop-blur-sm"
                >
                  <div className="absolute top-2 right-2 w-6 h-6 bg-blue-500/10 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-300"></div>
                  <div className="absolute -top-1 -right-1 w-4 h-4 bg-indigo-500/10 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-300"></div>
                  
                  <Link to={`/admin/${viewingAsSector || role}/account/${acc.id}`} className="block">
                    <div className="relative overflow-hidden">
                      <div className="pt-4 px-4">
                        <img
                          src={`http://localhost:5000/uploads/${acc.image_path}`}
                          alt={acc.name}
                          className="w-full h-32 object-cover transition-transform duration-300 group-hover:scale-105 rounded-xl"
                        />
                      </div>
                      <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-black/5 to-transparent group-hover:from-black/40 transition-all duration-300 rounded-xl mt-4 mx-4"></div>
                      
                      <div className="absolute inset-0 bg-gradient-to-br from-blue-600/10 via-indigo-600/5 to-blue-600/10 opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center rounded-xl mt-4 mx-4">
                        <div className="text-white text-center transform translate-y-2 group-hover:translate-y-0 transition-transform duration-300">
                          <div className="bg-white/30 backdrop-blur-sm rounded-full p-2 mb-2 mx-auto w-8 h-8 flex items-center justify-center">
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                          </div>
                          <p className="text-xs font-medium">View Details</p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="p-5 space-y-4">
                      <div>
                        <h3 className="text-lg font-bold text-gray-800 group-hover:text-blue-600 transition-colors duration-300 mb-2">
                          {acc.name}
                        </h3>
                        <div className="relative">
                          <p className="text-gray-600 text-sm leading-relaxed line-clamp-2">
                            {acc.description || "No description available"}
                          </p>
                          {acc.description && acc.description.length > 100 && (
                            <button className="text-blue-600 hover:text-blue-700 text-xs font-medium mt-1 transition-colors duration-200">
                              Read more
                            </button>
                          )}
                        </div>
                      </div>
                      
                      <div className="space-y-2">
                        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-3 border border-blue-100">
                          <div className="flex items-center text-gray-600 text-sm">
                            <div className="bg-blue-100 p-1.5 rounded-lg mr-3 group-hover:bg-blue-200 transition-colors duration-300">
                              <svg className="w-3.5 h-3.5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                                />
                              </svg>
                            </div>
                            <div className="flex-1">
                              <p className="text-xs text-blue-600 font-semibold mb-1">Account Created</p>
                              <div className="flex items-center justify-between">
                                <p className="font-medium text-gray-700">
                                  {(() => {
                                    const createdDate = new Date(acc.created_at);
                                    const now = new Date();
                                    const diffTime = Math.abs(now - createdDate);
                                    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                                    
                                    if (diffDays === 1) return "Today";
                                    if (diffDays === 2) return "Yesterday";
                                    if (diffDays <= 7) return `${diffDays - 1} days ago`;
                                    if (diffDays <= 30) return `${Math.ceil((diffDays - 1) / 7)} weeks ago`;
                                    if (diffDays <= 365) return `${Math.ceil((diffDays - 1) / 30)} months ago`;
                                    return `${Math.ceil((diffDays - 1) / 365)} years ago`;
                                  })()}
                                </p>
                                <p className="text-xs text-gray-500">
                                  {new Date(acc.created_at).toLocaleDateString('en-US', { 
                                    month: 'short', 
                                    day: 'numeric',
                                    year: new Date(acc.created_at).getFullYear() !== new Date().getFullYear() ? 'numeric' : undefined
                                  })}
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Link>
                  
                  <div className="px-5 pb-5">
                    <button
                      onClick={(e) => {
                        e.preventDefault()
                        e.stopPropagation()
                        showConfirmDelete(acc.id)
                      }}
                      className="w-full bg-red-50 hover:bg-red-500 text-red-600 hover:text-white font-medium py-3 px-4 rounded-xl transition-all duration-300 flex items-center justify-center group/delete border border-red-200 hover:border-red-500 hover:shadow-lg hover:shadow-red-500/20 transform hover:scale-[1.01]"
                    >
                      <svg className="w-4 h-4 text-red-600 group-hover/delete:text-white group-hover/delete:scale-110 transition-all duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                        />
                      </svg>
                    </button>
                  </div>
                </div>
              ))          )}
        </div>

          {toast.visible && (
            <div
              className={`fixed top-4 right-4 px-4 py-3 rounded-xl shadow-xl text-white transform transition-all duration-500 backdrop-blur-sm border-l-4 z-50 ${
                toast.type === "success" 
                  ? "bg-gradient-to-r from-green-500 to-emerald-600 border-green-400 shadow-green-500/25" 
                  : "bg-gradient-to-r from-red-500 to-rose-600 border-red-400 shadow-red-500/25"
              } animate-pulse`}
            >
              <div className="flex items-center space-x-2">
                <div className={`p-1.5 rounded-full ${toast.type === "success" ? "bg-white/20" : "bg-white/20"}`}>
                  {toast.type === "success" ? (
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  ) : (
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  )}
                </div>
                <div>
                  <p className="font-semibold text-sm">{toast.message}</p>
                  <p className="text-xs opacity-90">
                    {toast.type === "success" ? "Operation completed successfully" : "Please try again"}
                  </p>
                </div>
              </div>
            </div>
          )}          {confirmDelete.visible && (
            <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4 animate-fadeIn">
              <div className="bg-white/95 backdrop-blur-xl rounded-2xl shadow-xl shadow-red-500/20 border border-white/20 p-6 max-w-sm w-full transform transition-all duration-500 scale-95 animate-scaleIn relative overflow-hidden">
                <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-red-400/10 to-pink-400/10 rounded-full blur-2xl"></div>
                <div className="absolute bottom-0 left-0 w-12 h-12 bg-gradient-to-br from-orange-400/10 to-red-400/10 rounded-full blur-xl"></div>
                
                <div className="text-center relative z-10">
                  <div className="w-16 h-16 bg-gradient-to-br from-red-100 to-red-200 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                    <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                      />
                    </svg>
                  </div>
                  <h3 className="text-lg font-bold text-black mb-2">Delete Account</h3>
                  <p className="text-black/80 mb-6 text-sm leading-relaxed">
                    Are you sure you want to permanently delete this account? This action cannot be undone and all associated data will be lost.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-3">
                    <button
                      onClick={hideConfirmDelete}
                      className="flex-1 bg-slate-100 hover:bg-slate-200 text-slate-700 font-semibold py-3 px-4 rounded-xl transition-all duration-300 border border-slate-200 hover:border-slate-300 group"
                    >
                      <span className="flex items-center justify-center text-sm">
                        <svg className="w-4 h-4 mr-1 group-hover:-translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                        Cancel
                      </span>
                    </button>
                    <button
                      onClick={() => handleDelete(confirmDelete.id)}
                      className="flex-1 bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white font-semibold py-3 px-4 rounded-xl shadow-lg shadow-red-500/25 transition-all duration-300 transform hover:scale-[1.01] hover:shadow-xl hover:shadow-red-500/40 group"
                    >
                      <span className="flex items-center justify-center text-sm">
                        <svg className="w-4 h-4 mr-1 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                        Delete Forever
                      </span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  )
}

export default Dashboard
