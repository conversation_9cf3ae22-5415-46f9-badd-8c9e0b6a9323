import { useState, useEffect } from "react"
import { use<PERSON>ara<PERSON>, useNavigate, Link } from "react-router-dom"
import axios from "axios"

const AccountDetails = () => {
  const { id, role } = useParams()
  const navigate = useNavigate()
  const [account, setAccount] = useState(null)
  const [opportunities, setOpportunities] = useState([])
  const [profiles, setProfiles] = useState([])
  const [loading, setLoading] = useState(true)
  const [toast, setToast] = useState({ message: "", type: "", visible: false })
  const [activeTab, setActiveTab] = useState("opportunities")
  const [showOpportunityForm, setShowOpportunityForm] = useState(false)
  const [confirmDeleteOpportunity, setConfirmDeleteOpportunity] = useState({ id: null, visible: false })
  const [confirmDeleteProfile, setConfirmDeleteProfile] = useState({ id: null, visible: false })
  const [opportunityForm, setOpportunityForm] = useState({
    name: "",
    description: "",
    sector: localStorage.getItem("role") || "",
  })

  const formatSectorName = (sectorName) => {
    if (!sectorName) return "Not specified"
    const lowerSector = sectorName.toLowerCase()
    switch (lowerSector) {
      case 'bfsi':
        return 'BFSI'
      case 'defense':
        return 'Defense'
      case 'telco':
        return 'Telco'
      case 'enterprise':
        return 'Enterprise'
      default:
        return sectorName.charAt(0).toUpperCase() + sectorName.slice(1)
    }
  }

  const showToast = (message, type) => {
    setToast({ message, type, visible: true })
    setTimeout(() => setToast({ message: "", type: "", visible: false }), 3000)
  }

  const showConfirmDeleteOpportunity = (id) => {
    setConfirmDeleteOpportunity({ id, visible: true })
  }

  const hideConfirmDeleteOpportunity = () => {
    setConfirmDeleteOpportunity({ id: null, visible: false })
  }

  const showConfirmDeleteProfile = (id) => {
    setConfirmDeleteProfile({ id, visible: true })
  }

  const hideConfirmDeleteProfile = () => {
    setConfirmDeleteProfile({ id: null, visible: false })
  }

  const fetchAccount = async () => {
    try {
      const res = await axios.get(`http://localhost:5000/api/accounts/${id}`)
      setAccount(res.data)
    } catch (error) {
      console.error("Error fetching account:", error)
      showToast("Error fetching account details", "error")
    }
  }

  const fetchOpportunities = async () => {
    try {
      const res = await axios.get(`http://localhost:5000/api/opportunities/account/${id}`)
      setOpportunities(res.data)
    } catch (error) {
      console.error("Error fetching opportunities:", error)
      showToast("Error fetching opportunities", "error")
    }
  }

  const fetchProfiles = async () => {
    try {
      const res = await axios.get(`http://localhost:5000/api/profiles/account/${id}`)
      setProfiles(res.data)
    } catch (error) {
      console.error("Error fetching profiles:", error)
      showToast("Error fetching profiles", "error")
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchAccount()
    fetchOpportunities()
    fetchProfiles()
  }, [id])

  const handleOpportunitySubmit = async (e) => {
    e.preventDefault()

    try {
      console.log("Sending opportunity data:", {
        ...opportunityForm,
        account_id: id,
      })

      const response = await axios.post("http://localhost:5000/api/opportunities/add", {
        ...opportunityForm,
        account_id: id,
      })

      console.log("Response:", response.data)

      showToast("Opportunity added successfully!", "success")
      setOpportunityForm({
        name: "",
        description: "",
        sector: localStorage.getItem("role") || "",
      })
      setShowOpportunityForm(false)
      fetchOpportunities()
    } catch (error) {
      console.error("Error adding opportunity:", error)
      showToast(`Error adding opportunity: ${error.message}`, "error")
    }
  }

  const handleDeleteOpportunity = async (opportunityId) => {
    hideConfirmDeleteOpportunity()
    try {
      await axios.delete(`http://localhost:5000/api/opportunities/delete/${opportunityId}`)
      showToast("Opportunity deleted successfully!", "success")
      fetchOpportunities()
    } catch (error) {
      console.error("Error deleting opportunity:", error)
      showToast("Error deleting opportunity", "error")
    }
  }

  const handleDeleteProfile = async (profileId) => {
    hideConfirmDeleteProfile()
    try {
      await axios.delete(`http://localhost:5000/api/profiles/delete/${profileId}`)
      showToast("Profile deleted successfully!", "success")
      fetchProfiles()
    } catch (error) {
      console.error("Error deleting profile:", error)
      showToast("Error deleting profile", "error")
    }
  }

  const goBack = () => {
    navigate(`/admin/${role}`)
  }

  const logout = () => {
    localStorage.clear()
    
    sessionStorage.clear()
    
    navigate("/", { replace: true })
    
    window.history.pushState(null, null, "/")
    
    window.addEventListener('popstate', function(event) {
      window.history.pushState(null, null, "/")
    })
  }

  const navigateToFishbone = (opportunityId) => {
    navigate(`/admin/${role}/account/${id}/opportunity/${opportunityId}`)
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center relative overflow-hidden bg-gradient-to-br from-slate-900 via-blue-900 to-purple-900">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="absolute inset-0 bg-black/10"></div>
        
        <div className="bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl shadow-blue-200/50 border border-white/20 p-12 relative z-10">
          <div className="flex flex-col items-center space-y-6">
            <div className="relative">
              <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-200 border-t-blue-600"></div>
              <div className="absolute inset-0 rounded-full bg-gradient-to-r from-blue-500/20 to-indigo-500/20 blur-xl"></div>
            </div>
            <div className="text-center">
              <div className="text-xl font-bold text-slate-800 mb-2">Loading Account Details</div>
              <div className="text-slate-600 text-sm">Please wait while we fetch your data...</div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (!account) {
    return (
      <div className="min-h-screen flex items-center justify-center relative overflow-hidden bg-gradient-to-br from-slate-900 via-blue-900 to-purple-900">
        <div className="absolute inset-0 bg-black/10"></div>
        
        <div className="bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl shadow-red-200/50 border border-white/20 p-12 text-center relative z-10">
          <div className="w-20 h-20 bg-gradient-to-br from-red-100 to-red-200 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg">
            <svg className="w-10 h-10 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </div>
          <div className="text-xl font-bold text-slate-800 mb-3">Account Not Found</div>
          <div className="text-slate-600 mb-8 text-sm">The account you're looking for doesn't exist or has been removed.</div>
          <button
            onClick={() => navigate(-1)}
            className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-bold px-8 py-4 rounded-2xl shadow-xl shadow-blue-500/25 transition-all duration-300 transform hover:scale-105"
          >
            Go Back
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen p-4 md:p-8 relative overflow-hidden bg-gradient-to-br from-slate-900 via-blue-900 to-purple-900">
      <div className="absolute inset-0 bg-black/10"></div>

      <div className="max-w-6xl mx-auto relative z-10">
        <div className="flex flex-col sm:flex-row justify-between items-center mb-12 space-y-4 sm:space-y-0">
          <button
            onClick={goBack}
            className="group flex items-center text-slate-600 hover:text-blue-600 transition-all duration-300 font-medium bg-white/70 backdrop-blur-sm px-6 py-3 rounded-full shadow-sm hover:shadow-md border border-white/20"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-3 group-hover:-translate-x-1 transition-transform duration-300" viewBox="0 0 20 20" fill="currentColor">
              <path
                fillRule="evenodd"
                d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z"
                clipRule="evenodd"
              />
            </svg>
            Back to Dashboard
          </button>
          <button
            onClick={logout}
            className="bg-gradient-to-r from-slate-600 to-slate-700 hover:from-slate-700 hover:to-slate-800 text-white font-bold px-6 py-3 rounded-2xl shadow-lg transition-all duration-300 transform hover:scale-105 flex items-center group"
          >
            <div className="bg-white/20 p-1.5 rounded-lg mr-3 group-hover:bg-white/30 transition-colors duration-300">
              <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
              </svg>
            </div>
            Logout
          </button>
        </div>

        <div className="bg-white rounded-3xl shadow-2xl shadow-blue-200/50 border border-white/20 p-8 md:p-12 mb-12 relative overflow-hidden">
          
          <div className="flex flex-col lg:flex-row gap-8 relative z-10">
            <div className="w-full lg:w-1/3">
              <div className="relative group">
                <img
                  src={`http://localhost:5000/uploads/${account.image_path}`}
                  alt={account.name}
                  className="w-full h-72 object-cover rounded-3xl shadow-2xl transition-transform duration-500 group-hover:scale-105"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-black/10 to-transparent rounded-3xl group-hover:from-black/60 transition-all duration-500"></div>
              </div>
            </div>
            
            <div className="w-full lg:w-2/3 space-y-8">
              <div>
                <h1 className="text-2xl lg:text-3xl font-bold bg-gradient-to-r from-slate-800 via-blue-800 to-indigo-800 bg-clip-text text-transparent mb-4">
                  {account.name}
                </h1>
                <p className="text-slate-600 text-sm leading-relaxed mb-6">
                  {account.description || "No description available for this account."}
                </p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="group bg-slate-50/80 backdrop-blur-sm rounded-2xl p-6 border border-slate-200/50 hover:bg-white/80 transition-all duration-300">
                  <div className="flex items-center mb-3">
                    <div className="bg-blue-100 p-2 rounded-xl mr-3 group-hover:bg-blue-200 transition-colors duration-300">
                      <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                    </div>
                    <h3 className="text-xs font-bold text-slate-600 uppercase tracking-wide">Address</h3>
                  </div>
                  <p className="text-slate-800 font-medium text-sm">{account.address || "Not specified"}</p>
                </div>
                
                <div className="group bg-slate-50/80 backdrop-blur-sm rounded-2xl p-6 border border-slate-200/50 hover:bg-white/80 transition-all duration-300">
                  <div className="flex items-center mb-3">
                    <div className="bg-indigo-100 p-2 rounded-xl mr-3 group-hover:bg-indigo-200 transition-colors duration-300">
                      <svg className="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                      </svg>
                    </div>
                    <h3 className="text-xs font-bold text-slate-600 uppercase tracking-wide">Type</h3>
                  </div>
                  <p className="text-slate-800 font-medium text-sm">{account.type || "Not specified"}</p>
                </div>
                
                <div className="group bg-slate-50/80 backdrop-blur-sm rounded-2xl p-6 border border-slate-200/50 hover:bg-white/80 transition-all duration-300">
                  <div className="flex items-center mb-3">
                    <div className="bg-purple-100 p-2 rounded-xl mr-3 group-hover:bg-purple-200 transition-colors duration-300">
                      <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h4M9 7h6m-6 4h6m-2 4h4" />
                      </svg>
                    </div>
                    <h3 className="text-xs font-bold text-slate-600 uppercase tracking-wide">Sector</h3>
                  </div>
                  <p className="text-slate-800 font-medium text-sm">{formatSectorName(account.sector)}</p>
                </div>
                
                <div className="group bg-slate-50/80 backdrop-blur-sm rounded-2xl p-6 border border-slate-200/50 hover:bg-white/80 transition-all duration-300">
                  <div className="flex items-center mb-3">
                    <div className="bg-green-100 p-2 rounded-xl mr-3 group-hover:bg-green-200 transition-colors duration-300">
                      <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <h3 className="text-xs font-bold text-slate-600 uppercase tracking-wide">Created</h3>
                  </div>
                  <p className="text-slate-800 font-medium text-sm">{new Date(account.created_at).toLocaleDateString()}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-3xl shadow-xl shadow-blue-200/50 border border-white/20 p-2 mb-8 relative overflow-hidden">
          <div className="flex relative z-10">
            <button
              className={`flex-1 px-8 py-4 font-bold text-sm rounded-2xl transition-all duration-300 flex items-center justify-center space-x-3 ${
                activeTab === "opportunities"
                  ? "bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg transform scale-[1.02]"
                  : "text-slate-600 hover:text-blue-600 hover:bg-blue-50/50"
              }`}
              onClick={() => setActiveTab("opportunities")}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
              </svg>
              <span>Opportunities</span>
              <span className={`px-2 py-1 rounded-full text-xs font-bold ${
                activeTab === "opportunities" 
                  ? "bg-white/20 text-white" 
                  : "bg-blue-100 text-blue-600"
              }`}>
                {opportunities.length}
              </span>
            </button>
            <button
              className={`flex-1 px-8 py-4 font-bold text-sm rounded-2xl transition-all duration-300 flex items-center justify-center space-x-3 ${
                activeTab === "profiles"
                  ? "bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg transform scale-[1.02]"
                  : "text-slate-600 hover:text-blue-600 hover:bg-blue-50/50"
              }`}
              onClick={() => setActiveTab("profiles")}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              <span>Profiles</span>
              <span className={`px-2 py-1 rounded-full text-xs font-bold ${
                activeTab === "profiles" 
                  ? "bg-white/20 text-white" 
                  : "bg-blue-100 text-blue-600"
              }`}>
                {profiles.length}
              </span>
            </button>
          </div>
        </div>

        {activeTab === "opportunities" && (
          <div className="bg-white rounded-3xl shadow-2xl shadow-blue-200/50 border border-white/20 p-8 md:p-12 relative overflow-hidden">
            <div className="flex flex-col lg:flex-row justify-between items-center mb-12 space-y-6 lg:space-y-0 relative z-10">
              <div>
                <h2 className="text-xl lg:text-2xl font-bold bg-gradient-to-r from-slate-800 to-blue-800 bg-clip-text text-transparent">
                  Business Opportunities
                </h2>
                <p className="text-slate-600 mt-2 text-sm">Manage and track your business opportunities</p>
              </div>
              <button
                onClick={() => setShowOpportunityForm(!showOpportunityForm)}
                className="bg-gradient-to-r from-green-600 via-green-700 to-emerald-700 hover:from-green-700 hover:via-green-800 hover:to-emerald-800 text-white font-bold px-8 py-4 rounded-2xl shadow-xl shadow-green-500/25 transition-all duration-300 flex items-center transform hover:scale-105 hover:shadow-2xl hover:shadow-green-500/40 group"
              >
                <div className="bg-white/20 p-2 rounded-xl mr-3 group-hover:bg-white/30 transition-colors duration-300">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className={`h-5 w-5 transition-transform duration-300 ${showOpportunityForm ? 'rotate-45' : 'group-hover:rotate-90'}`}
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                {showOpportunityForm ? 'Cancel' : 'Add Opportunity'}
              </button>
            </div>

            {showOpportunityForm && (
              <div className="bg-slate-50/80 backdrop-blur-sm rounded-3xl p-8 mb-12 border-2 border-slate-200/50 hover:border-blue-300/50 transition-all duration-300 relative overflow-hidden">
                <div className="relative z-10">
                  <div className="flex items-center mb-8">
                    <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
                      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-lg font-bold text-slate-800">Create New Opportunity</h3>
                      <p className="text-slate-600 text-sm">Add a new business opportunity to track</p>
                    </div>
                  </div>
                  
                  <form onSubmit={handleOpportunitySubmit} className="space-y-8">
                    <div className="group">
                      <label className="text-xs font-bold text-slate-700 mb-3 flex items-center">
                        <svg className="w-4 h-4 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        Opportunity Name *
                      </label>
                      <input
                        type="text"
                        className="w-full border-2 border-slate-200 rounded-2xl p-4 text-slate-700 bg-white/80 focus:outline-none focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 focus:bg-white transition-all duration-300 group-hover:border-slate-300 text-sm"
                        placeholder="Enter a descriptive opportunity name"
                        value={opportunityForm.name}
                        onChange={(e) => setOpportunityForm({ ...opportunityForm, name: e.target.value })}
                        required
                      />
                    </div>
                    
                    <div className="group">
                      <label className="text-xs font-bold text-slate-700 mb-3 flex items-center">
                        <svg className="w-4 h-4 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h7" />
                        </svg>
                        Description *
                      </label>
                      <textarea
                        className="w-full border-2 border-slate-200 rounded-2xl p-4 text-slate-700 bg-white/80 focus:outline-none focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 focus:bg-white transition-all duration-300 resize-y group-hover:border-slate-300 text-sm"
                        rows="4"
                        placeholder="Provide detailed information about the opportunity..."
                        value={opportunityForm.description}
                        onChange={(e) => setOpportunityForm({ ...opportunityForm, description: e.target.value })}
                        required
                      />
                    </div>
                    
                    <div className="flex flex-col sm:flex-row gap-4 pt-4">
                      <button
                        type="button"
                        onClick={() => setShowOpportunityForm(false)}
                        className="flex-1 bg-slate-100 hover:bg-slate-200 text-slate-700 font-bold py-4 px-6 rounded-2xl transition-all duration-300 border-2 border-slate-200 hover:border-slate-300 group"
                      >
                        <span className="flex items-center justify-center">
                          <svg className="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                          Cancel
                        </span>
                      </button>
                      <button
                        type="submit"
                        className="flex-1 bg-gradient-to-r from-green-600 via-green-700 to-emerald-700 hover:from-green-700 hover:via-green-800 hover:to-emerald-800 text-white font-bold py-4 px-6 rounded-2xl shadow-xl shadow-green-500/25 transition-all duration-300 transform hover:scale-[1.02] hover:shadow-2xl hover:shadow-green-500/40 group"
                      >
                        <span className="flex items-center justify-center">
                          <svg className="w-5 h-5 mr-2 group-hover:rotate-90 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                          Save Opportunity
                        </span>
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            )}

            {opportunities.length === 0 ? (
              <div className="text-center py-20 relative z-10">
                <div className="bg-white rounded-3xl shadow-xl shadow-blue-200/50 border border-white/20 p-16 max-w-lg mx-auto relative overflow-hidden">
                  <div className="relative z-10">
                    <div className="w-20 h-20 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-lg">
                      <svg className="w-10 h-10 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                        />
                      </svg>
                    </div>
                    <h3 className="text-lg font-bold text-slate-800 mb-3">No opportunities yet</h3>
                    <p className="text-slate-600 text-sm mb-8 leading-relaxed">Start tracking your business opportunities by creating your first one.</p>
                    <button
                      onClick={() => setShowOpportunityForm(true)}
                      className="inline-flex items-center bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-bold px-8 py-4 rounded-2xl shadow-xl shadow-green-500/25 transition-all duration-300 transform hover:scale-105 hover:shadow-2xl hover:shadow-green-500/40 group"
                    >
                      <svg className="w-5 h-5 mr-2 group-hover:rotate-90 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                      Create Your First Opportunity
                    </button>
                  </div>
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 relative z-10">
                {opportunities.map((opportunity) => (
                  <div
                    key={opportunity.id}
                    className="group bg-white/90 backdrop-blur-sm rounded-3xl border-2 border-slate-200/50 hover:border-blue-300/50 overflow-hidden transition-all duration-500 hover:shadow-2xl hover:shadow-blue-300/60 transform hover:scale-105 relative"
                  >
                    <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-blue-400/10 to-indigo-400/10 rounded-full blur-xl group-hover:w-20 group-hover:h-20 transition-all duration-500"></div>
                    
                    <div className="p-8 relative z-10">
                      <div className="flex justify-between items-start mb-6">
                        <div className="flex-1">
                          <h3 className="text-xl font-bold text-slate-800 group-hover:text-blue-700 transition-colors duration-300 mb-2">
                            {opportunity.name}
                          </h3>
                          <p className="text-slate-600 text-sm leading-relaxed">
                            {opportunity.description || "No description available"}
                          </p>
                        </div>
                        <button
                          onClick={() => showConfirmDeleteOpportunity(opportunity.id)}
                          className="ml-4 p-2 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-xl transition-all duration-300 group/delete"
                          title="Delete opportunity"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-5 w-5 group-hover/delete:scale-110 transition-transform duration-300"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path
                              fillRule="evenodd"
                              d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </button>
                      </div>
                      
                      <div className="mb-6">
                        <div className="inline-flex items-center px-3 py-1.5 bg-blue-100 text-blue-800 rounded-full text-xs font-bold">
                          <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h4M9 7h6m-6 4h6m-2 4h4" />
                          </svg>
                          {formatSectorName(opportunity.sector)}
                        </div>
                      </div>
                      
                      <button
                        onClick={() => navigateToFishbone(opportunity.id)}
                        className="w-full bg-gradient-to-r from-blue-600 via-blue-700 to-indigo-700 hover:from-blue-700 hover:via-blue-800 hover:to-indigo-800 text-white font-bold py-4 px-6 rounded-2xl shadow-xl shadow-blue-500/25 transition-all duration-300 transform hover:scale-[1.02] hover:shadow-2xl hover:shadow-blue-500/40 group/button"
                      >
                        <span className="flex items-center justify-center">
                          <svg className="w-5 h-5 mr-2 group-hover/button:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
                          </svg>
                          View Mind Map
                        </span>
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {activeTab === "profiles" && (
          <div className="bg-white rounded-3xl shadow-2xl shadow-blue-200/50 border border-white/20 p-8 md:p-12 relative overflow-hidden">
            <div className="flex flex-col lg:flex-row justify-between items-center mb-12 space-y-6 lg:space-y-0 relative z-10">
              <div>
                <h2 className="text-xl lg:text-2xl font-bold bg-gradient-to-r from-slate-800 to-purple-800 bg-clip-text text-transparent">
                  Client Profiles
                </h2>
                <p className="text-slate-600 mt-2 text-sm">Manage your client contacts and relationships</p>
              </div>
              <Link
                to={`/admin/${role}/account/${id}/add-profile`}
                className="bg-gradient-to-r from-purple-600 via-purple-700 to-indigo-700 hover:from-purple-700 hover:via-purple-800 hover:to-indigo-800 text-white font-bold px-8 py-4 rounded-2xl shadow-xl shadow-purple-500/25 transition-all duration-300 flex items-center transform hover:scale-105 hover:shadow-2xl hover:shadow-purple-500/40 group"
              >
                <div className="bg-white/20 p-2 rounded-xl mr-3 group-hover:bg-white/30 transition-colors duration-300">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 group-hover:rotate-90 transition-transform duration-300"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                Add Profile
              </Link>
            </div>

            {profiles.length === 0 ? (
              <div className="text-center py-20 relative z-10">
                <div className="bg-white rounded-3xl shadow-xl shadow-purple-200/50 border border-white/20 p-16 max-w-lg mx-auto relative overflow-hidden">
                  <div className="relative z-10">
                    <div className="w-20 h-20 bg-gradient-to-br from-purple-100 to-indigo-100 rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-lg">
                      <svg className="w-10 h-10 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                        />
                      </svg>
                    </div>
                    <h3 className="text-lg font-bold text-slate-800 mb-3">No profiles yet</h3>
                    <p className="text-slate-600 text-sm mb-8 leading-relaxed">Build your network by adding client profiles and contacts.</p>
                    <Link
                      to={`/admin/${role}/account/${id}/add-profile`}
                      className="inline-flex items-center bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white font-bold px-8 py-4 rounded-2xl shadow-xl shadow-purple-500/25 transition-all duration-300 transform hover:scale-105 hover:shadow-2xl hover:shadow-purple-500/40 group"
                    >
                      <svg className="w-5 h-5 mr-2 group-hover:rotate-90 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                      Add Your First Profile
                    </Link>
                  </div>
                </div>
              </div>
            ) : (
              <div className="bg-white rounded-2xl border border-slate-200/50 overflow-hidden shadow-lg relative z-10">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gradient-to-r from-slate-50 to-slate-100 border-b border-slate-200">
                      <tr>
                        <th className="px-6 py-4 text-center text-xs font-bold text-slate-700 uppercase tracking-wider">Profile</th>
                        <th className="px-6 py-4 text-center text-xs font-bold text-slate-700 uppercase tracking-wider">Position</th>
                        <th className="px-6 py-4 text-center text-xs font-bold text-slate-700 uppercase tracking-wider">Department</th>
                        <th className="px-6 py-4 text-center text-xs font-bold text-slate-700 uppercase tracking-wider">Contact</th>
                        <th className="px-6 py-4 text-center text-xs font-bold text-slate-700 uppercase tracking-wider">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-slate-200">
                      {profiles.map((profile, index) => (
                        <tr 
                          key={profile.id} 
                          className={`group hover:bg-slate-50/80 transition-colors duration-200 ${
                            index % 2 === 0 ? 'bg-white' : 'bg-slate-50/30'
                          }`}
                        >                          <td className="px-6 py-4 whitespace-nowrap text-center">
                            <div className="flex items-center justify-center">
                              <div className="relative mr-4">
                                {profile.profile_photo ? (
                                  <img
                                    src={`http://localhost:5000/uploads/profiles/${profile.profile_photo}`}
                                    alt={profile.full_name}
                                    className="w-12 h-12 rounded-full object-cover border-2 border-white shadow-md group-hover:shadow-lg transition-shadow duration-200"
                                  />
                                ) : (
                                  <div className="w-12 h-12 rounded-full bg-gradient-to-br from-purple-100 to-indigo-100 flex items-center justify-center border-2 border-white shadow-md group-hover:shadow-lg transition-shadow duration-200">
                                    <svg
                                      className="h-6 w-6 text-purple-600"
                                      fill="none"
                                      viewBox="0 0 24 24"
                                      stroke="currentColor"
                                    >
                                      <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                                      />
                                    </svg>
                                  </div>
                                )}
                                <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white flex items-center justify-center">
                                  <svg className="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                  </svg>
                                </div>
                              </div>
                              <div>
                                <div className="text-sm font-bold text-slate-900 group-hover:text-purple-700 transition-colors duration-200">
                                  {profile.full_name}
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-center">
                            <div className="text-sm font-medium text-purple-600">
                              {profile.designation}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-center">
                            <div className="text-sm text-slate-700">
                              {profile.department}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-center">
                            <div className="space-y-1 flex flex-col items-center">
                              <div className="flex items-center text-xs text-slate-600">
                                <div className="bg-blue-100 p-1 rounded-md mr-2">
                                  <svg className="h-3 w-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                  </svg>
                                </div>
                                <span className="truncate max-w-[200px]">{profile.work_email}</span>
                              </div>
                              <div className="flex items-center text-xs text-slate-600">
                                <div className="bg-green-100 p-1 rounded-md mr-2">
                                  <svg className="h-3 w-3 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                  </svg>
                                </div>
                                <span>{profile.office_number}</span>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-center">
                            <div className="flex items-center justify-center space-x-2">
                              <Link
                                to={`/admin/${role}/account/${id}/profile/${profile.id}`}
                                className="inline-flex items-center px-3 py-1.5 bg-purple-100 hover:bg-purple-200 text-purple-700 hover:text-purple-800 text-xs font-medium rounded-lg transition-colors duration-200 group/view"
                              >
                                <svg className="w-3 h-3 mr-1 group-hover/view:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                </svg>
                                View
                              </Link>
                              <button
                                onClick={(e) => {
                                  e.preventDefault();
                                  showConfirmDeleteProfile(profile.id);
                                }}
                                className="inline-flex items-center px-3 py-1.5 bg-red-100 hover:bg-red-200 text-red-700 hover:text-red-800 text-xs font-medium rounded-lg transition-colors duration-200 group/delete"
                                title="Delete Profile"
                              >
                                <svg className="w-3 h-3 mr-1 group-hover/delete:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                                Delete
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {confirmDeleteOpportunity.visible && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-white/95 backdrop-blur-xl rounded-2xl shadow-xl shadow-red-500/20 border border-white/20 p-6 max-w-sm w-full transform transition-all duration-500 scale-95 relative overflow-hidden">
            <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-red-400/10 to-pink-400/10 rounded-full blur-2xl"></div>
            <div className="absolute bottom-0 left-0 w-12 h-12 bg-gradient-to-br from-orange-400/10 to-red-400/10 rounded-full blur-xl"></div>
            
            <div className="text-center relative z-10">
              <div className="w-16 h-16 bg-gradient-to-br from-red-100 to-red-200 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
              </div>
              <h3 className="text-lg font-bold text-black mb-2">Delete Opportunity</h3>
              <p className="text-black/80 mb-6 text-sm leading-relaxed">
                Are you sure you want to permanently delete this opportunity? This action cannot be undone and all associated data will be lost.
              </p>
              <div className="flex flex-col sm:flex-row gap-3">
                <button
                  onClick={hideConfirmDeleteOpportunity}
                  className="flex-1 bg-slate-100 hover:bg-slate-200 text-slate-700 font-semibold py-3 px-4 rounded-xl transition-all duration-300 border border-slate-200 hover:border-slate-300 group"
                >
                  <span className="flex items-center justify-center text-sm">
                    <svg className="w-4 h-4 mr-1 group-hover:-translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                    Cancel
                  </span>
                </button>
                <button
                  onClick={() => handleDeleteOpportunity(confirmDeleteOpportunity.id)}
                  className="flex-1 bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white font-semibold py-3 px-4 rounded-xl shadow-lg shadow-red-500/25 transition-all duration-300 transform hover:scale-[1.01] hover:shadow-xl hover:shadow-red-500/40 group"
                >
                  <span className="flex items-center justify-center text-sm">
                    <svg className="w-4 h-4 mr-1 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                    Delete Forever
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {confirmDeleteProfile.visible && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-white/95 backdrop-blur-xl rounded-2xl shadow-xl shadow-red-500/20 border border-white/20 p-6 max-w-sm w-full transform transition-all duration-500 scale-95 relative overflow-hidden">
            <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-red-400/10 to-pink-400/10 rounded-full blur-2xl"></div>
            <div className="absolute bottom-0 left-0 w-12 h-12 bg-gradient-to-br from-orange-400/10 to-red-400/10 rounded-full blur-xl"></div>
            
            <div className="text-center relative z-10">
              <div className="w-16 h-16 bg-gradient-to-br from-red-100 to-red-200 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
              <h3 className="text-lg font-bold text-black mb-2">Delete Profile</h3>
              <p className="text-black/80 mb-6 text-sm leading-relaxed">
                Are you sure you want to permanently delete this profile? This action cannot be undone and all contact information will be lost.
              </p>
              <div className="flex flex-col sm:flex-row gap-3">
                <button
                  onClick={hideConfirmDeleteProfile}
                  className="flex-1 bg-slate-100 hover:bg-slate-200 text-slate-700 font-semibold py-3 px-4 rounded-xl transition-all duration-300 border border-slate-200 hover:border-slate-300 group"
                >
                  <span className="flex items-center justify-center text-sm">
                    <svg className="w-4 h-4 mr-1 group-hover:-translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                    Cancel
                  </span>
                </button>
                <button
                  onClick={() => handleDeleteProfile(confirmDeleteProfile.id)}
                  className="flex-1 bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white font-semibold py-3 px-4 rounded-xl shadow-lg shadow-red-500/25 transition-all duration-300 transform hover:scale-[1.01] hover:shadow-xl hover:shadow-red-500/40 group"
                >
                  <span className="flex items-center justify-center text-sm">
                    <svg className="w-4 h-4 mr-1 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                    Delete Forever
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {toast.visible && (
        <div
          className={`fixed top-6 right-6 px-6 py-4 rounded-2xl shadow-2xl text-white transform transition-all duration-500 backdrop-blur-sm border-l-4 z-50 ${
            toast.type === "success" 
              ? "bg-gradient-to-r from-green-500 to-emerald-600 border-green-400 shadow-green-500/25" 
              : "bg-gradient-to-r from-red-500 to-rose-600 border-red-400 shadow-red-500/25"
          } animate-pulse`}
        >
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-full ${toast.type === "success" ? "bg-white/20" : "bg-white/20"}`}>
              {toast.type === "success" ? (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              ) : (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              )}
            </div>
            <div>
              <p className="font-semibold text-sm">{toast.message}</p>
              <p className="text-xs opacity-90">
                {toast.type === "success" ? "Operation completed successfully" : "Please try again"}
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default AccountDetails
