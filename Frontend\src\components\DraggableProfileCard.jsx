import { useNavigate, useParams } from "react-router-dom"
import { useState } from "react"

const DraggableProfileCard = ({ profile, id }) => {
  const navigate = useNavigate()
  const { id: accountId, role } = useParams()
  const [isDragging, setIsDragging] = useState(false)
  const [imageError, setImageError] = useState(false)

  const handleDragStart = (e) => {
    setIsDragging(true)
    
    e.dataTransfer.setData(
      "text/plain",
      JSON.stringify({
        type: "profile",
        profileId: profile.id,
      }),
    )

    e.dataTransfer.effectAllowed = "copy"

    const ghostElement = e.currentTarget.cloneNode(true)
    ghostElement.style.position = "absolute"
    ghostElement.style.top = "-1000px"
    ghostElement.style.transform = "scale(1.05)"
    ghostElement.style.opacity = "0.8"
    document.body.appendChild(ghostElement)
    e.dataTransfer.setDragImage(ghostElement, 15, 15)

    setTimeout(() => {
      document.body.removeChild(ghostElement)
      setIsDragging(false)
    }, 100)
  }

  const handleDragEnd = () => {
    setIsDragging(false)
  }

  const handleDoubleClick = (e) => {
    e.preventDefault()
    e.stopPropagation()

    navigate(`/admin/${role}/account/${accountId}/profile/${profile.id}`)
  }

  const handleImageError = () => {
    setImageError(true)
  }

  const getInitials = (name) => {
    return name
      ?.split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2) || '??'
  }

  const getAvatarColor = (name) => {
    const colors = [
      'bg-blue-500',
      'bg-purple-500',
      'bg-green-500',
      'bg-pink-500',
      'bg-indigo-500',
      'bg-teal-500',
    ]
    const index = name ? name.charCodeAt(0) % colors.length : 0
    return colors[index]
  }

  const getRoleColors = (role) => {
    switch (role) {
      case 'Decision Maker':
        return {
          border: 'border-red-300 hover:border-red-400',
          background: 'bg-red-50',
          badge: 'bg-red-100 text-red-700',
          accent: 'bg-red-500'
        }
      case 'Influencer':
        return {
          border: 'border-blue-300 hover:border-blue-400',
          background: 'bg-blue-50',
          badge: 'bg-blue-100 text-blue-700',
          accent: 'bg-blue-500'
        }
      case 'User':
        return {
          border: 'border-green-300 hover:border-green-400',
          background: 'bg-green-50',
          badge: 'bg-green-100 text-green-700',
          accent: 'bg-green-500'
        }
      case 'Gatekeeper':
        return {
          border: 'border-purple-300 hover:border-purple-400',
          background: 'bg-purple-50',
          badge: 'bg-purple-100 text-purple-700',
          accent: 'bg-purple-500'
        }
      default:
        return {
          border: 'border-gray-200 hover:border-gray-300',
          background: 'bg-white',
          badge: 'bg-gray-100 text-gray-700',
          accent: 'bg-gray-500'
        }
    }
  }

  const roleColors = getRoleColors(profile.decision_role)

  return (
    <div
      draggable="true"
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      onDoubleClick={handleDoubleClick}
      className={`
        group relative ${roleColors.background} rounded-lg shadow-sm hover:shadow-md 
        border-2 ${roleColors.border}
        p-2 transition-all duration-200 ease-in-out
        cursor-grab active:cursor-grabbing hover:cursor-pointer
        transform hover:scale-[1.01] 
        ${isDragging ? 'opacity-50 scale-95' : ''}
      `}
      title="Double-click to view • Drag to add"
    >
      <div className={`absolute -top-1 -right-1 ${roleColors.accent} text-white rounded-full w-4 h-4 flex items-center justify-center opacity-80 group-hover:opacity-100 transition-opacity`}>
        <svg xmlns="http://www.w3.org/2000/svg" className="h-2 w-2" viewBox="0 0 20 20" fill="currentColor">
          <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z" />
        </svg>
      </div>

      <div className="flex items-center space-x-2">
        {profile.profile_photo && !imageError ? (
          <img
            src={`http://localhost:5000/uploads/profiles/${profile.profile_photo}`}
            alt={profile.full_name}
            className="w-8 h-8 rounded-full object-cover border border-gray-200 group-hover:border-blue-300 transition-colors"
            onError={handleImageError}
          />
        ) : (
          <div className={`w-8 h-8 rounded-full ${getAvatarColor(profile.full_name)} flex items-center justify-center text-white text-xs font-semibold`}>
            {getInitials(profile.full_name)}
          </div>
        )}
        
        <div className="flex-1 min-w-0">
          <h3 className="font-medium text-xs text-gray-900 truncate group-hover:text-gray-800 transition-colors">
            {profile.full_name}
          </h3>
          
          {profile.designation && (
            <p className="text-xs text-gray-500 truncate mt-0.5">
              {profile.designation}
            </p>
          )}
        </div>

        <div className="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity">
          <svg className="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </div>
      </div>
    </div>
  )
}

export default DraggableProfileCard
