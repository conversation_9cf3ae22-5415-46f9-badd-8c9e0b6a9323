import { useState, useEffect } from "react"
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom"
import axios from "axios"
import SuperAdminHeader from "../components/SuperAdminHeader"

const ProfileDetails = () => {
  const { id, profileId, role } = useParams()
  const navigate = useNavigate()
  const [profile, setProfile] = useState(null)
  const [account, setAccount] = useState(null)
  const [loading, setLoading] = useState(true)
  const [toast, setToast] = useState({ message: "", type: "", visible: false })
  const [activeTab, setActiveTab] = useState("personal")
  const viewingAsSector = localStorage.getItem("viewing_as_sector")

  const showToast = (message, type) => {
    setToast({ message, type, visible: true })
    setTimeout(() => setToast({ message: "", type: "", visible: false }), 3000)
  }

  const fetchProfile = async () => {
    try {
      const res = await axios.get(`http://localhost:5000/api/profiles/${profileId}`)
      setProfile(res.data)
    } catch (error) {
      console.error("Error fetching profile:", error)
      showToast("Error fetching profile details", "error")
    }
  }

  const fetchAccount = async () => {
    try {
      const res = await axios.get(`http://localhost:5000/api/accounts/${id}`)
      setAccount(res.data)
    } catch (error) {
      console.error("Error fetching account:", error)
      showToast("Error fetching account details", "error")
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchProfile()
    fetchAccount()
  }, [profileId, id])

  const goBack = () => {
    navigate(`/admin/${viewingAsSector || role}/account/${id}`)
  }

  if (loading) {
    return (
      <>
        <SuperAdminHeader />
        <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-purple-900 flex items-center justify-center relative overflow-hidden">
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute top-20 left-20 w-64 h-64 bg-blue-200/20 rounded-full blur-3xl animate-pulse"></div>
            <div className="absolute bottom-32 right-16 w-80 h-80 bg-indigo-200/15 rounded-full blur-3xl animate-pulse delay-1000"></div>
          </div>
          
          <div className="bg-white rounded-2xl shadow-xl p-8 relative z-10">
            <div className="flex flex-col items-center space-y-3">
              <div className="w-12 h-12 border-4 border-blue-500/30 border-t-blue-600 rounded-full animate-spin"></div>
              <div className="text-xl font-bold text-gray-800">
                Loading Profile...
              </div>
            </div>
          </div>
        </div>
      </>
    )
  }

  if (!profile) {
    return (
      <>
        <SuperAdminHeader />
        <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-purple-900 flex items-center justify-center relative overflow-hidden">
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute top-20 left-20 w-64 h-64 bg-red-200/20 rounded-full blur-3xl animate-pulse"></div>
            <div className="absolute bottom-32 right-16 w-80 h-80 bg-rose-200/15 rounded-full blur-3xl animate-pulse delay-1000"></div>
          </div>
          
          <div className="bg-white rounded-2xl shadow-xl p-8 relative z-10">
            <div className="flex flex-col items-center space-y-3">
              <svg className="w-12 h-12 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <div className="text-xl font-bold text-gray-800">
                Profile not found
              </div>
            </div>
          </div>
        </div>
      </>
    )
  }

  const authorityLevels = profile.authority_level ? profile.authority_level.split(",") : []

  return (
    <>
      <SuperAdminHeader />
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-purple-900 p-3 md:p-6 relative overflow-hidden">
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-20 left-20 w-64 h-64 bg-blue-200/20 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-32 right-16 w-80 h-80 bg-indigo-200/15 rounded-full blur-3xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-56 h-56 bg-purple-200/15 rounded-full blur-2xl animate-pulse delay-500"></div>
        </div>

        <div className="max-w-6xl mx-auto relative z-10">
          <button 
            onClick={goBack} 
            className="mb-6 group flex items-center text-gray-300 hover:text-white transition-all duration-300 bg-white/10 hover:bg-white/20 backdrop-blur-sm rounded-xl px-4 py-3 border border-gray-200/20 hover:border-blue-300/30"
          >
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              className="h-4 w-4 mr-2 group-hover:-translate-x-1 transition-transform duration-300" 
              viewBox="0 0 20 20" 
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z"
                clipRule="evenodd"
              />
            </svg>
            <span className="font-semibold">Back to Account</span>
          </button>

          <div className="bg-white rounded-2xl shadow-xl p-6 md:p-8 mb-6 overflow-hidden relative">
            <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-blue-400/10 to-indigo-400/10 rounded-full blur-2xl"></div>
            <div className="absolute bottom-0 left-0 w-20 h-20 bg-gradient-to-br from-purple-400/10 to-pink-400/10 rounded-full blur-xl"></div>
            
            <div className="flex flex-col lg:flex-row gap-6 md:gap-8 relative z-10">
              <div className="w-full lg:w-1/3 flex flex-col items-center">
                {profile.profile_photo ? (
                  <div className="relative group">
                    <img
                      src={`http://localhost:5000/uploads/profiles/${profile.profile_photo}`}
                      alt={profile.full_name}
                      className="w-40 h-40 object-cover rounded-2xl shadow-xl border-4 border-white/50 transition-transform duration-300 group-hover:scale-105"
                    />
                    <div className="absolute inset-0 rounded-2xl bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  </div>
                ) : (
                  <div className="w-40 h-40 rounded-2xl bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center shadow-xl border-4 border-white/50">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-20 w-20 text-gray-400"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={1.5}
                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                      />
                    </svg>
                  </div>
                )}
                
                <div className="mt-4 text-center">
                  <h1 className="text-2xl md:text-3xl font-bold text-gray-800 mb-1">
                    {profile.full_name}
                  </h1>
                  <p className="text-lg text-gray-700 font-semibold">{profile.designation}</p>
                  <p className="text-gray-600 font-medium">{profile.department}</p>
                  
                  <button
                    onClick={() => navigate(`/admin/${viewingAsSector || role}/account/${id}/profile/${profileId}/edit`)}
                    className="mt-4 group flex items-center justify-center mx-auto bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl hover:shadow-blue-200/50 transform hover:-translate-y-0.5"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4 mr-2 group-hover:rotate-12 transition-transform duration-300"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                      />
                    </svg>
                    Edit Profile
                  </button>
                </div>
                
                <div className="mt-6 w-full max-w-sm space-y-3">
                  <div className="flex items-center text-sm bg-white/60 backdrop-blur-sm rounded-xl p-3 border border-gray-200/50 hover:bg-white/80 transition-all duration-300">
                    <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-4 w-4 text-white"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                        />
                      </svg>
                    </div>
                    <span className="text-gray-700 font-medium">{profile.work_email}</span>
                  </div>
                  
                  <div className="flex items-center text-sm bg-white/60 backdrop-blur-sm rounded-xl p-3 border border-gray-200/50 hover:bg-white/80 transition-all duration-300">
                    <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center mr-3">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-4 w-4 text-white"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                        />
                      </svg>
                    </div>
                    <span className="text-gray-700 font-medium">{profile.office_number}</span>
                  </div>
                  
                  <div className="flex items-center text-sm bg-white/60 backdrop-blur-sm rounded-xl p-3 border border-gray-200/50 hover:bg-white/80 transition-all duration-300">
                    <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mr-3">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-4 w-4 text-white"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                        />
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                        />
                      </svg>
                    </div>
                    <span className="text-gray-700 font-medium">{profile.office_location}</span>
                  </div>
                  
                  {profile.linkedin_url && (
                    <div className="flex items-center text-sm bg-white/60 backdrop-blur-sm rounded-xl p-3 border border-gray-200/50 hover:bg-white/80 transition-all duration-300">
                      <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-blue-700 rounded-lg flex items-center justify-center mr-3">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-4 w-4 text-white"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101"
                          />
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M10.172 13.828a4 4 0 015.656 0l4 4a4 4 0 01-5.656 5.656l-1.102-1.101"
                          />
                        </svg>
                      </div>
                      <a
                        href={profile.linkedin_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800 font-medium hover:underline transition-colors duration-300"
                      >
                        LinkedIn Profile
                      </a>
                    </div>
                  )}
                </div>
              </div>
              <div className="w-full lg:w-2/3">
                <div className="bg-white/60 backdrop-blur-sm rounded-xl border border-gray-200/50 p-2 mb-6">
                  <div className="flex flex-wrap gap-1">
                    <button
                      className={`flex-1 min-w-0 px-4 py-3 font-semibold text-sm rounded-lg transition-all duration-300 ${
                        activeTab === "personal"
                          ? "bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg"
                          : "text-gray-600 hover:text-blue-600 hover:bg-white/60"
                      }`}
                      onClick={() => setActiveTab("personal")}
                    >
                      <span className="truncate">Personal Information</span>
                    </button>
                    <button
                      className={`flex-1 min-w-0 px-4 py-3 font-semibold text-sm rounded-lg transition-all duration-300 ${
                        activeTab === "professional"
                          ? "bg-gradient-to-r from-indigo-500 to-purple-600 text-white shadow-lg"
                          : "text-gray-600 hover:text-indigo-600 hover:bg-white/60"
                      }`}
                      onClick={() => setActiveTab("professional")}
                    >
                      <span className="truncate">Professional Background</span>
                    </button>
                    <button
                      className={`flex-1 min-w-0 px-4 py-3 font-semibold text-sm rounded-lg transition-all duration-300 ${
                        activeTab === "decision"
                          ? "bg-gradient-to-r from-purple-500 to-pink-600 text-white shadow-lg"
                          : "text-gray-600 hover:text-purple-600 hover:bg-white/60"
                      }`}
                      onClick={() => setActiveTab("decision")}
                    >
                      <span className="truncate">Decision Process</span>
                    </button>
                  </div>
                </div>

                {activeTab === "personal" && (
                  <div className="bg-gradient-to-br from-white/90 to-blue-50/30 backdrop-blur-sm rounded-2xl p-8 border border-gray-200/50 shadow-xl shadow-blue-100/20">
                    <div className="flex items-center mb-6">
                      <div className="w-2 h-8 bg-gradient-to-b from-blue-500 to-blue-600 rounded-full mr-4"></div>
                      <h2 className="text-2xl font-bold bg-gradient-to-r from-gray-800 to-blue-800 bg-clip-text text-transparent">Personal Details</h2>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {profile.date_of_birth && (
                        <div className="bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-gray-200/50 hover:bg-white/80 transition-all duration-300">
                          <h3 className="text-sm font-bold text-blue-600 mb-2 flex items-center">
                            <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                            Date of Birth
                          </h3>
                          <p className="text-gray-800 font-medium">{new Date(profile.date_of_birth).toLocaleDateString()}</p>
                        </div>
                      )}
                      {profile.marital_status && (
                        <div className="bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-gray-200/50 hover:bg-white/80 transition-all duration-300">
                          <h3 className="text-sm font-bold text-blue-600 mb-2 flex items-center">
                            <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                            Marital Status
                          </h3>
                          <p className="text-gray-800 font-medium">{profile.marital_status}</p>
                        </div>
                      )}
                      {profile.children && (
                        <div className="bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-gray-200/50 hover:bg-white/80 transition-all duration-300">
                          <h3 className="text-sm font-bold text-blue-600 mb-2 flex items-center">
                            <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                            Children
                          </h3>
                          <p className="text-gray-800 font-medium">{profile.children}</p>
                        </div>
                      )}
                      {profile.personal_email && (
                        <div className="bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-gray-200/50 hover:bg-white/80 transition-all duration-300">
                          <h3 className="text-sm font-bold text-blue-600 mb-2 flex items-center">
                            <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                            Personal Email
                          </h3>
                          <p className="text-gray-800 font-medium">{profile.personal_email}</p>
                        </div>
                      )}
                      {profile.mobile_number && (
                        <div className="bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-gray-200/50 hover:bg-white/80 transition-all duration-300">
                          <h3 className="text-sm font-bold text-blue-600 mb-2 flex items-center">
                            <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                            Mobile Number
                          </h3>
                          <p className="text-gray-800 font-medium">{profile.mobile_number}</p>
                        </div>
                      )}
                      {profile.hobbies && (
                        <div className="bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-gray-200/50 hover:bg-white/80 transition-all duration-300">
                          <h3 className="text-sm font-bold text-blue-600 mb-2 flex items-center">
                            <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                            Hobbies / Interests
                          </h3>
                          <p className="text-gray-800 font-medium">{profile.hobbies}</p>
                        </div>
                      )}
                      {profile.clubs && (
                        <div className="bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-gray-200/50 hover:bg-white/80 transition-all duration-300">
                          <h3 className="text-sm font-bold text-blue-600 mb-2 flex items-center">
                            <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                            Clubs / Associations
                          </h3>
                          <p className="text-gray-800 font-medium">{profile.clubs}</p>
                        </div>
                      )}
                      {profile.education && (
                        <div className="bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-gray-200/50 hover:bg-white/80 transition-all duration-300">
                          <h3 className="text-sm font-bold text-blue-600 mb-2 flex items-center">
                            <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                            Education
                          </h3>
                          <p className="text-gray-800 font-medium">{profile.education}</p>
                        </div>
                      )}
                      {profile.personality_traits && (
                        <div className="bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-gray-200/50 hover:bg-white/80 transition-all duration-300 md:col-span-2">
                          <h3 className="text-sm font-bold text-blue-600 mb-2 flex items-center">
                            <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                            Personality Traits
                          </h3>
                          <p className="text-gray-800 font-medium">{profile.personality_traits}</p>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {activeTab === "professional" && (
                  <div className="bg-white/90 backdrop-blur-sm rounded-xl p-6 border border-gray-200/50 shadow-lg">
                    <div className="flex items-center mb-4">
                      <div className="w-2 h-6 bg-gradient-to-b from-indigo-500 to-purple-600 rounded-full mr-3"></div>
                      <h2 className="text-xl font-bold text-gray-800">Professional Background</h2>
                    </div>
                    
                    <div className="space-y-4">
                      {profile.career_history && (
                        <div className="bg-white/60 backdrop-blur-sm rounded-lg p-4 border border-gray-200/50 hover:bg-white/80 transition-all duration-300">
                          <h3 className="text-base font-bold text-indigo-600 mb-2 flex items-center">
                            <span className="w-2 h-2 bg-indigo-500 rounded-full mr-2"></span>
                            Career History
                          </h3>
                          <p className="text-gray-800 leading-relaxed whitespace-pre-line">{profile.career_history}</p>
                        </div>
                      )}
                      {profile.key_responsibilities && (
                        <div className="bg-white/60 backdrop-blur-sm rounded-lg p-4 border border-gray-200/50 hover:bg-white/80 transition-all duration-300">
                          <h3 className="text-base font-bold text-indigo-600 mb-2 flex items-center">
                            <span className="w-2 h-2 bg-indigo-500 rounded-full mr-2"></span>
                            Key Responsibilities
                          </h3>
                          <p className="text-gray-800 leading-relaxed whitespace-pre-line">{profile.key_responsibilities}</p>
                        </div>
                      )}
                      {profile.kpis && (
                        <div className="bg-white/60 backdrop-blur-sm rounded-lg p-4 border border-gray-200/50 hover:bg-white/80 transition-all duration-300">
                          <h3 className="text-base font-bold text-indigo-600 mb-2 flex items-center">
                            <span className="w-2 h-2 bg-indigo-500 rounded-full mr-2"></span>
                            KPIs / Performance Goals
                          </h3>
                          <p className="text-gray-800 leading-relaxed whitespace-pre-line">{profile.kpis}</p>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {activeTab === "decision" && (
                  <div className="bg-white/90 backdrop-blur-sm rounded-xl p-6 border border-gray-200/50 shadow-lg">
                    <div className="flex items-center mb-4">
                      <div className="w-2 h-6 bg-gradient-to-b from-purple-500 to-pink-600 rounded-full mr-3"></div>
                      <h2 className="text-xl font-bold text-gray-800">Decision Process Role</h2>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {profile.decision_role && (
                        <div className="bg-white/60 backdrop-blur-sm rounded-lg p-4 border border-gray-200/50 hover:bg-white/80 transition-all duration-300">
                          <h3 className="text-sm font-bold text-purple-600 mb-2 flex items-center">
                            <span className="w-2 h-2 bg-purple-500 rounded-full mr-2"></span>
                            Decision Role
                          </h3>
                          <span className="inline-flex items-center px-3 py-1 rounded-lg text-sm font-semibold bg-gradient-to-r from-green-500 to-emerald-600 text-white shadow-md">
                            {profile.decision_role}
                          </span>
                        </div>
                      )}
                      {profile.reports_to && (
                        <div className="bg-white/60 backdrop-blur-sm rounded-lg p-4 border border-gray-200/50 hover:bg-white/80 transition-all duration-300">
                          <h3 className="text-sm font-bold text-purple-600 mb-2 flex items-center">
                            <span className="w-2 h-2 bg-purple-500 rounded-full mr-2"></span>
                            Reports To
                          </h3>
                          <p className="text-gray-800 font-medium">{profile.reports_to}</p>
                        </div>
                      )}
                      {profile.influenced_by && (
                        <div className="bg-white/60 backdrop-blur-sm rounded-lg p-4 border border-gray-200/50 hover:bg-white/80 transition-all duration-300">
                          <h3 className="text-sm font-bold text-purple-600 mb-2 flex items-center">
                            <span className="w-2 h-2 bg-purple-500 rounded-full mr-2"></span>
                            Influenced By
                          </h3>
                          <p className="text-gray-800 font-medium">{profile.influenced_by}</p>
                        </div>
                      )}
                      {profile.has_influence_over && (
                        <div className="bg-white/60 backdrop-blur-sm rounded-lg p-4 border border-gray-200/50 hover:bg-white/80 transition-all duration-300">
                          <h3 className="text-sm font-bold text-purple-600 mb-2 flex items-center">
                            <span className="w-2 h-2 bg-purple-500 rounded-full mr-2"></span>
                            Has Influence Over
                          </h3>
                          <p className="text-gray-800 font-medium">{profile.has_influence_over}</p>
                        </div>
                      )}
                      {authorityLevels.length > 0 && (
                        <div className="bg-white/60 backdrop-blur-sm rounded-lg p-4 border border-gray-200/50 hover:bg-white/80 transition-all duration-300 md:col-span-2">
                          <h3 className="text-sm font-bold text-purple-600 mb-2 flex items-center">
                            <span className="w-2 h-2 bg-purple-500 rounded-full mr-2"></span>
                            Level of Authority
                          </h3>
                          <div className="flex flex-wrap gap-2">
                            {authorityLevels.map((level) => (
                              <span
                                key={level}
                                className="inline-flex items-center px-3 py-1 rounded-lg text-sm font-semibold bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-md hover:shadow-lg transition-shadow duration-300"
                              >
                                {level}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {toast.visible && (
          <div className="fixed top-4 right-4 z-50 animate-slide-in-right">
            <div
              className={`px-4 py-3 rounded-xl shadow-xl backdrop-blur-xl border text-white transform transition-all duration-500 ${
                toast.type === "success" 
                  ? "bg-gradient-to-r from-green-500/90 to-emerald-600/90 border-green-300/30" 
                  : "bg-gradient-to-r from-red-500/90 to-rose-600/90 border-red-300/30"
              }`}
            >
              <div className="flex items-center space-x-2">
                {toast.type === "success" ? (
                  <svg className="w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                ) : (
                  <svg className="w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                )}
                <span className="font-medium text-sm">{toast.message}</span>
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  )
}

export default ProfileDetails
