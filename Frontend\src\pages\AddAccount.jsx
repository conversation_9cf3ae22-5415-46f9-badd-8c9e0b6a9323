import { useState } from "react"
import { useNavigate, useParams } from "react-router-dom"
import axios from "axios"

const AddAccount = () => {
  const [form, setForm] = useState({
    name: "",
    description: "",
    address: "",
    type: "",
    sector: localStorage.getItem("role"),
  })
  const [image, setImage] = useState(null)
  const [imagePreview, setImagePreview] = useState(null)
  const [isLoading, setIsLoading] = useState(false)
  const [toast, setToast] = useState({ message: "", type: "", visible: false })
  const adminId = localStorage.getItem("admin_id")
  const { role } = useParams()
  const navigate = useNavigate()

  const showToast = (message, type) => {
    setToast({ message, type, visible: true })
    setTimeout(() => setToast({ message: "", type: "", visible: false }), 3000)
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsLoading(true)
    
    const data = new FormData()
    for (const key in form) data.append(key, form[key])
    data.append("admin_id", adminId)
    if (image) data.append("image", image)

    try {
      await axios.post("http://localhost:5000/api/accounts/add", data)
      showToast("Account created successfully!", "success")
      setTimeout(() => navigate(`/admin/${role}`), 1500)
    } catch (err) {
      console.error(err)
      showToast("Error adding account", "error")
    } finally {
      setIsLoading(false)
    }
  }

  const handleImageChange = (e) => {
    const file = e.target.files[0]
    if (file) {
      setImage(file)
      const reader = new FileReader()
      reader.onload = (e) => setImagePreview(e.target.result)
      reader.readAsDataURL(file)
    }
  }

  const goBack = () => {
    navigate(`/admin/${role}`)
  }

  return (
    <div 
      className="min-h-screen p-4 md:p-8 relative overflow-hidden"
      style={{
        backgroundImage: 'url(https://img.freepik.com/free-vector/gradient-abstract-wireframe-background_23-**********.jpg?semt=ais_hybrid&w=740)',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }}
    >
      <div className="absolute inset-0 bg-black/20"></div>

      <div className="max-w-3xl mx-auto relative z-10">
        <div className="mb-8">
          <button
            onClick={goBack}
            className="group flex items-center text-white hover:text-blue-300 transition-all duration-300 mb-4 font-medium bg-white/20 backdrop-blur-sm px-3 py-2 rounded-full shadow-sm hover:shadow-md text-sm"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 group-hover:-translate-x-1 transition-transform duration-300" viewBox="0 0 20 20" fill="currentColor">
              <path
                fillRule="evenodd"
                d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z"
                clipRule="evenodd"
              />
            </svg>
            Back to Dashboard
          </button>
          
          <div className="text-center">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl mb-4 shadow-lg">
              <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
            </div>
            <h1 className="text-3xl font-bold text-white mb-3">
              Add New Account
            </h1>
            <p className="text-lg text-white/80 max-w-xl mx-auto leading-relaxed">
              Create a new account to manage your projects and opportunities with enhanced features
            </p>
          </div>
        </div>

        <div className="bg-white backdrop-blur-xl rounded-2xl shadow-xl shadow-blue-200/50 border border-white/20 p-6 md:p-8 relative overflow-hidden">
          <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-blue-400/10 to-indigo-400/10 rounded-full blur-2xl"></div>
          <div className="absolute bottom-0 left-0 w-20 h-20 bg-gradient-to-br from-purple-400/10 to-pink-400/10 rounded-full blur-xl"></div>
          <form onSubmit={handleSubmit} className="space-y-6 relative z-10">
            <div className="group">
              <label className="text-xs font-bold text-slate-700 mb-2 flex items-center">
                <svg className="w-3 h-3 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h4M9 7h6m-6 4h6m-2 4h4" />
                </svg>
                Account Name *
              </label>
              <div className="relative">
                <input
                  type="text"
                  className="w-full border-2 border-slate-200 rounded-xl p-3 pl-10 text-slate-700 bg-slate-50/50 focus:outline-none focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 focus:bg-white transition-all duration-300 group-hover:border-slate-300"
                  onChange={(e) => setForm({ ...form, name: e.target.value })}
                  required
                />
                <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                  <svg className="w-4 h-4 text-slate-400 group-focus-within:text-blue-500 transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                </div>
              </div>
            </div>

            <div className="group">
              <label className="text-xs font-bold text-slate-700 mb-2 flex items-center">
                <svg className="w-3 h-3 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Description
              </label>
              <textarea
                className="w-full border-2 border-slate-200 rounded-xl p-3 text-slate-700 bg-slate-50/50 focus:outline-none focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 focus:bg-white transition-all duration-300 resize-y group-hover:border-slate-300"
                rows="3"
                onChange={(e) => setForm({ ...form, description: e.target.value })}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="group">
                <label className="text-xs font-bold text-slate-700 mb-2 flex items-center">
                  <svg className="w-3 h-3 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  Address
                </label>
                <div className="relative">
                  <input
                    type="text"
                    className="w-full border-2 border-slate-200 rounded-xl p-3 pl-10 text-slate-700 bg-slate-50/50 focus:outline-none focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 focus:bg-white transition-all duration-300 group-hover:border-slate-300"
                    onChange={(e) => setForm({ ...form, address: e.target.value })}
                  />
                  <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                    <svg className="w-4 h-4 text-slate-400 group-focus-within:text-blue-500 transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h4M9 7h6m-6 4h6m-2 4h4" />
                    </svg>
                  </div>
                </div>
              </div>

              <div className="group">
                <label className="text-xs font-bold text-slate-700 mb-2 flex items-center">
                  <svg className="w-3 h-3 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                  </svg>
                  Type
                </label>
                <div className="relative">
                  <input
                    type="text"
                    className="w-full border-2 border-slate-200 rounded-xl p-3 pl-10 text-slate-700 bg-slate-50/50 focus:outline-none focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 focus:bg-white transition-all duration-300 group-hover:border-slate-300"
                    onChange={(e) => setForm({ ...form, type: e.target.value })}
                  />
                  <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                    <svg className="w-5 h-5 text-slate-400 group-focus-within:text-blue-500 transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>

            <div className="group">
              <label className="text-sm font-bold text-slate-700 mb-3 flex items-center">
                <svg className="w-4 h-4 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                Account Image
              </label>
              <div className="relative border-2 border-dashed border-slate-300 rounded-2xl p-8 text-center hover:border-blue-400 transition-all duration-300 group-hover:bg-blue-50/30">
                <input
                  type="file"
                  accept="image/*"
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                  onChange={handleImageChange}
                />
                {imagePreview ? (
                  <div className="space-y-4">
                    <img 
                      src={imagePreview} 
                      alt="Preview" 
                      className="mx-auto w-32 h-32 object-cover rounded-2xl shadow-lg"
                    />
                    <p className="text-slate-600 font-medium">Image selected - click to change</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    <div className="mx-auto w-16 h-16 bg-blue-100 rounded-2xl flex items-center justify-center group-hover:bg-blue-200 transition-colors duration-300">
                      <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                      </svg>
                    </div>
                    <div>
                      <p className="text-slate-600 font-medium">Drop your image here or click to browse</p>
                      <p className="text-sm text-slate-400 mt-1">PNG, JPG, GIF up to 10MB</p>
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-3 pt-6">
              <button
                type="button"
                onClick={goBack}
                className="flex-1 bg-slate-100 hover:bg-slate-200 text-slate-700 font-semibold py-3 px-4 rounded-xl transition-all duration-300 border border-slate-200 hover:border-slate-300 group"
              >
                <span className="flex items-center justify-center text-sm">
                  <svg className="w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                  Cancel
                </span>
              </button>
              <button
                type="submit"
                disabled={isLoading}
                className="flex-1 bg-gradient-to-r from-blue-600 via-blue-700 to-indigo-700 hover:from-blue-700 hover:via-blue-800 hover:to-indigo-800 disabled:from-slate-400 disabled:to-slate-500 text-white font-semibold py-3 px-4 rounded-xl shadow-lg shadow-blue-500/25 transition-all duration-300 transform hover:scale-[1.01] hover:shadow-xl hover:shadow-blue-500/40 group disabled:hover:scale-100 disabled:cursor-not-allowed"
              >
                <span className="flex items-center justify-center text-sm">
                  {isLoading ? (
                    <>
                      <svg className="w-4 h-4 mr-2 animate-spin" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Creating Account...
                    </>
                  ) : (
                    <>
                      <svg className="w-4 h-4 mr-2 group-hover:rotate-90 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                      Create Account
                    </>
                  )}
                </span>
              </button>
            </div>
          </form>
        </div>
      </div>

      {toast.visible && (
        <div
          className={`fixed top-4 right-4 px-4 py-3 rounded-xl shadow-xl text-white transform transition-all duration-500 backdrop-blur-sm border-l-4 z-50 ${
            toast.type === "success" 
              ? "bg-gradient-to-r from-green-500 to-emerald-600 border-green-400 shadow-green-500/25" 
              : "bg-gradient-to-r from-red-500 to-rose-600 border-red-400 shadow-red-500/25"
          } animate-pulse`}
        >
          <div className="flex items-center space-x-2">
            <div className={`p-1.5 rounded-full ${toast.type === "success" ? "bg-white/20" : "bg-white/20"}`}>
              {toast.type === "success" ? (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              ) : (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              )}
            </div>
            <div>
              <p className="font-semibold text-sm">{toast.message}</p>
              <p className="text-xs opacity-90">
                {toast.type === "success" ? "Operation completed successfully" : "Please try again"}
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default AddAccount
