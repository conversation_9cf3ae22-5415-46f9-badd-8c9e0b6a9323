import { useNavigate } from "react-router-dom"

const SuperAdminHeader = () => {
  const navigate = useNavigate()
  const viewingAsSector = localStorage.getItem("viewing_as_sector")

  const handleReturnToSuperAdmin = () => {
    const superadminId = localStorage.getItem("superadmin_id")
    const superadminUsername = localStorage.getItem("superadmin_username")

    if (superadminId && superadminUsername) {
      localStorage.setItem("admin_id", superadminId)
      localStorage.setItem("username", superadminUsername)
      localStorage.setItem("role", "superadmin")
      localStorage.removeItem("viewing_as_sector")
      localStorage.removeItem("superadmin_id")
      localStorage.removeItem("superadmin_username")

      navigate("/admin/superadmin")
    }
  }

  if (!viewingAsSector) {
    return null
  }

  return (
    <div className="bg-yellow-100 border-b border-yellow-300 p-2 text-center">
      <div className="flex justify-center items-center">
        <div className="text-yellow-800 font-medium">
          You are viewing as <span className="font-bold uppercase">{viewingAsSector}</span> admin
        </div>
        <button
          onClick={handleReturnToSuperAdmin}
          className="ml-4 bg-yellow-500 hover:bg-yellow-600 text-white px-3 py-1 rounded-md text-sm font-medium transition-colors"
        >
          Return to Super Admin
        </button>
      </div>
    </div>
  )
}

export default SuperAdminHeader
