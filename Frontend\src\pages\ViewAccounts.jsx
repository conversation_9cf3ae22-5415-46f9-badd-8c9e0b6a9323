import { useEffect, useState } from "react";
import axios from "axios";

const ViewAccounts = () => {
  const [accounts, setAccounts] = useState([]);
  const adminId = localStorage.getItem("admin_id");

  const fetchAccounts = async () => {
    const res = await axios.get(`http://localhost:5000/api/accounts/admin/${adminId}`);
    setAccounts(res.data);
  };

  useEffect(() => {
    fetchAccounts();
  }, []);

  const handleDelete = async (id) => {
    if (window.confirm("Delete this account?")) {
      await axios.delete(`http://localhost:5000/api/accounts/delete/${id}`);
      fetchAccounts();
    }
  };

  return (
    <div className="p-6 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
      {accounts.map((acc) => (
        <div key={acc.id} className="bg-white shadow p-4 rounded">
          <img
            src={`http://localhost:5000/uploads/${acc.image_path}`}
            alt={acc.name}
            className="w-full h-40 object-cover mb-2 rounded"
          />
          <h3 className="text-lg font-bold">{acc.name}</h3>
          <p className="text-sm">{acc.description}</p>
          <p className="text-gray-600 text-sm">{acc.address}</p>
          <p className="text-gray-500 text-xs mt-1">{acc.created_at}</p>
          <div className="flex justify-between mt-2">
            <button onClick={() => handleDelete(acc.id)} className="text-red-500">Delete</button>
          </div>
        </div>
      ))}
    </div>
  );
};

export default ViewAccounts;
