import { useState, useEffect } from "react"
import { useNavigate } from "react-router-dom"
import API from "../api"

const AdminManagement = () => {
  const navigate = useNavigate()
  const [admins, setAdmins] = useState([])
  const [loading, setLoading] = useState(true)
  const [toast, setToast] = useState({ message: "", type: "", visible: false })
  const [editingAdmin, setEditingAdmin] = useState(null)
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [adminToDelete, setAdminToDelete] = useState(null)
  const [formData, setFormData] = useState({
    username: "",
    password: "",
  })

  const [showCreateForm, setShowCreateForm] = useState(false)
  const [createFormData, setCreateFormData] = useState({
    username: "",
    password: "",
    role: "BFSI",
  })

  const showToast = (message, type) => {
    setToast({ message, type, visible: true })
    setTimeout(() => setToast({ message: "", type: "", visible: false }), 3000)
  }

  useEffect(() => {
    const role = localStorage.getItem("role")
    if (role !== "superadmin") {
      navigate("/")
      return
    }

    fetchAdmins()
  }, [navigate])

  const fetchAdmins = async () => {
    try {
      const response = await API.get("/admins/all")
      setAdmins(response.data)
      setLoading(false)
    } catch (error) {
      console.error("Error fetching admins:", error)
      showToast("Error fetching admins", "error")
      setLoading(false)
    }
  }

  const handleEditClick = (admin) => {
    setEditingAdmin(admin)
    setFormData({
      username: admin.username,
      password: "",
    })
  }

  const handleCancelEdit = () => {
    setEditingAdmin(null)
    setFormData({
      username: "",
      password: "",
    })
  }

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData({
      ...formData,
      [name]: value,
    })
  }

  const handleSubmit = async (e) => {
    e.preventDefault()

    if (!formData.username) {
      showToast("Username cannot be empty", "error")
      return
    }

    try {
      await API.put(`/admins/update/${editingAdmin.id}`, formData)
      showToast("Admin credentials updated successfully", "success")
      setEditingAdmin(null)
      fetchAdmins()
    } catch (error) {
      console.error("Error updating admin:", error)
      showToast(error.response?.data?.error || "Error updating admin", "error")
    }
  }

  const handleCreateClick = () => {
    setShowCreateForm(true)
    setCreateFormData({
      username: "",
      password: "",
      role: "BFSI",
    })
  }

  const handleCancelCreate = () => {
    setShowCreateForm(false)
    setCreateFormData({
      username: "",
      password: "",
      role: "BFSI",
    })
  }

  const handleCreateChange = (e) => {
    const { name, value } = e.target
    setCreateFormData({
      ...createFormData,
      [name]: value,
    })
  }

  const handleCreateSubmit = async (e) => {
    e.preventDefault()

    if (!createFormData.username || !createFormData.password || !createFormData.role) {
      showToast("All fields are required", "error")
      return
    }

    if (createFormData.password.length < 6) {
      showToast("Password must be at least 6 characters long", "error")
      return
    }

    try {
      await API.post("/admins/create", createFormData)
      showToast("Admin created successfully", "success")
      setShowCreateForm(false)
      fetchAdmins()
    } catch (error) {
      console.error("Error creating admin:", error)
      showToast(error.response?.data?.error || "Error creating admin", "error")
    }
  }

  const handleDeleteClick = (admin) => {
    setAdminToDelete(admin)
    setShowDeleteModal(true)
  }

  const confirmDelete = async () => {
    try {
      await API.delete(`/admins/delete/${adminToDelete.id}`)
      showToast("Admin deleted successfully", "success")
      setShowDeleteModal(false)
      fetchAdmins()
    } catch (error) {
      console.error("Error deleting admin:", error)
      showToast(error.response?.data?.error || "Error deleting admin", "error")
    }
  }

  const cancelDelete = () => {
    setShowDeleteModal(false)
    setAdminToDelete(null)
  }

  const logout = () => {
    localStorage.clear()
    
    sessionStorage.clear()
    
    navigate("/", { replace: true })
    
    window.history.pushState(null, null, "/")
    
    window.addEventListener('popstate', function(event) {
      window.history.pushState(null, null, "/")
    })
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="text-xl font-semibold text-gray-700">Loading...</div>
      </div>
    )
  }

  return (
    <div
      className="min-h-screen bg-gradient-to-br from-gray-100 to-gray-200 p-8 relative"
      style={{
        backgroundImage: `url(https://img.freepik.com/free-vector/gradient-abstract-wireframe-background_23-2149009903.jpg?semt=ais_hybrid&w=740)`,
        backgroundSize: "cover",
        backgroundPosition: "center",
      }}
    >
      <div
        className="absolute inset-0"
        style={{
          backdropFilter: "blur(10px)",
          zIndex: 0,
        }}
      ></div>

      <div className="max-w-6xl mx-auto relative z-10">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-white">Admin Management</h1>
            <p className="text-white mt-1">Manage admin users and credentials</p>
          </div>
          <div className="flex space-x-4">
            <button
              onClick={() => navigate("/admin/superadmin")}
              className="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-5 py-2.5 rounded-lg shadow-md transition-all duration-300"
            >
              Back to Dashboard
            </button>
            <button
              onClick={logout}
              className="bg-red-500 hover:bg-red-600 text-white font-semibold px-5 py-2.5 rounded-lg shadow-md transition-all duration-300"
            >
              Logout
            </button>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-8">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-gray-800">Admin Users</h2>
            <button
              onClick={handleCreateClick}
              className="bg-green-600 hover:bg-green-700 text-white font-medium px-4 py-2 rounded-lg shadow transition-colors flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                <path
                  fillRule="evenodd"
                  d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                  clipRule="evenodd"
                />
              </svg>
              Create New Admin
            </button>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full bg-white rounded-lg overflow-hidden">
              <thead className="bg-gray-100">
                <tr>
                  <th className="py-3 px-4 text-center text-gray-700 font-semibold">ID</th>
                  <th className="py-3 px-4 text-center text-gray-700 font-semibold">Username</th>
                  <th className="py-3 px-4 text-center text-gray-700 font-semibold">Role</th>
                  <th className="py-3 px-4 text-center text-gray-700 font-semibold">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {admins.map((admin) => (
                  <tr key={admin.id} className="hover:bg-gray-50">
                    <td className="py-3 px-4 text-gray-800 text-center">{admin.id}</td>
                    <td className="py-3 px-4 text-gray-800 text-center">{admin.username}</td>
                    <td className="py-3 px-4 text-center">
                      <span
                        className={`inline-block px-2 py-1 text-xs font-semibold rounded-full ${
                          admin.role === "superadmin"
                            ? "bg-purple-100 text-purple-800"
                            : admin.role === "BFSI"
                              ? "bg-blue-100 text-blue-800"
                              : admin.role === "Defense"
                                ? "bg-green-100 text-green-800"
                                : admin.role === "Telco"
                                  ? "bg-yellow-100 text-yellow-800"
                                  : "bg-gray-100 text-gray-800"
                        }`}>
                        {admin.role}
                      </span>
                    </td>
                    <td className="py-3 px-4 text-center">
                      <div className="flex justify-center space-x-4">
                        <button
                          onClick={() => handleEditClick(admin)}
                          className="text-blue-600 hover:text-blue-800 transition-colors"
                          title="Edit" >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                          </svg>
                        </button>
                        {admin.role !== "superadmin" && (
                          <button
                            onClick={() => handleDeleteClick(admin)}
                            className="text-red-600 hover:text-red-800 transition-colors"
                            title="Delete">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                              <path
                                fillRule="evenodd"
                                d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                                clipRule="evenodd"
                              />
                            </svg>
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {editingAdmin && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
              <div className="bg-white rounded-lg p-8 max-w-md w-full">
                <h3 className="text-xl font-bold text-gray-800 mb-4">Edit Admin: {editingAdmin.username}</h3>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Username</label>
                    <input
                      type="text"
                      name="username"
                      value={formData.username}
                      onChange={handleChange}
                      className="w-full border border-gray-300 rounded-lg p-2 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      New Password (leave blank to keep current)
                    </label>
                    <input
                      type="password"
                      name="password"
                      value={formData.password}
                      onChange={handleChange}
                      className="w-full border border-gray-300 rounded-lg p-2 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div className="flex justify-end space-x-3 pt-4">
                    <button
                      type="button"
                      onClick={handleCancelEdit}
                      className="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      Save Changes
                    </button>
                  </div>
                </form>
              </div>
            </div>
          )}

          {showCreateForm && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
              <div className="bg-white rounded-lg p-8 max-w-md w-full">
                <h3 className="text-xl font-bold text-gray-800 mb-4">Create New Admin</h3>
                <form onSubmit={handleCreateSubmit} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Username </label>
                    <input
                      type="text"
                      name="username"
                      value={createFormData.username}
                      onChange={handleCreateChange}
                      className="w-full border border-gray-300 rounded-lg p-2 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Password </label>
                    <input
                      type="password"
                      name="password"
                      value={createFormData.password}
                      onChange={handleCreateChange}
                      className="w-full border border-gray-300 rounded-lg p-2 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                      minLength="6"
                    />
                    <p className="text-xs text-gray-500 mt-1">Minimum 6 characters</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Role </label>
                    <select
                      name="role"
                      value={createFormData.role}
                      onChange={handleCreateChange}
                      className="w-full border border-gray-300 rounded-lg p-2 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    >
                      <option value="BFSI">BFSI</option>
                      <option value="Defense">Defense</option>
                      <option value="Telco">Telco</option>
                      <option value="Enterprise">Enterprise</option>
                      <option value="superadmin">Super Admin</option>
                    </select>
                  </div>
                  <div className="flex justify-end space-x-3 pt-4">
                    <button
                      type="button"
                      onClick={handleCancelCreate}
                      className="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors">
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                      Create Admin
                    </button>
                  </div>
                </form>
              </div>
            </div>
          )}
        </div>
      </div>

      {showDeleteModal && adminToDelete && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <div className="flex items-start justify-between mb-4">
              <h3 className="text-xl font-bold text-gray-800">Confirm Deletion</h3>
              <button 
                onClick={cancelDelete}
                className="text-gray-500 hover:text-gray-700">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <p className="text-gray-600 mb-6">
              Are you sure you want to delete admin <span className="font-semibold">"{adminToDelete.username}"</span>? 
              This action cannot be undone.
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={cancelDelete}
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors">
                Cancel
              </button>
              <button
                onClick={confirmDelete}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                Delete Admin
              </button>
            </div>
          </div>
        </div>
      )}

      {toast.visible && (
        <div className={`fixed top-4 right-4 px-6 py-3 rounded-lg shadow-lg text-white flex items-center transform transition-all duration-300 ${toast.type === "success" ? "bg-green-500" : "bg-red-500"}`}>
          {toast.type === "success" ? (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          ) : (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
          )}
          {toast.message}
        </div>
      )}
    </div>
  )
}

export default AdminManagement