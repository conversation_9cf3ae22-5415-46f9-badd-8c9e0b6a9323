import mysql from "mysql2"
import dotenv from "dotenv"
dotenv.config()

export const db = mysql.createConnection({
  host: process.env.DB_HOST || "localhost",
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || "root",
  password: process.env.DB_PASSWORD || "",
  database: process.env.DB_NAME || "mnconnect",
  multipleStatements: true,
})

db.connect((err) => {
  if (err) {
    console.error("❌ DB connection error:", err.message)
    console.error("Connection details:", {
      host: process.env.DB_HOST || "localhost",
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || "root",
      database: process.env.DB_NAME || "mnconnect",
    })

    console.log("Attempting to continue despite connection error...")
  } else {
    console.log("✅ Connected to MySQL")
  }
})

db.on("error", (err) => {
  console.error("Database error:", err)
  if (err.code === "PROTOCOL_CONNECTION_LOST") {
    console.log("Database connection was closed. Attempting to reconnect...")
    db.connect((reconnectErr) => {
      if (reconnectErr) {
        console.error("Failed to reconnect to database:", reconnectErr)
      } else {
        console.log("Successfully reconnected to database")
      }
    })
  }
})

export const queryPromise = (sql, params) => {
  return new Promise((resolve, reject) => {
    db.query(sql, params, (err, results) => {
      if (err) {
        console.error("Query error:", err, "SQL:", sql, "Params:", params)
        reject(err)
      } else {
        resolve(results)
      }
    })
  })
}

export const query = db.query.bind(db)
