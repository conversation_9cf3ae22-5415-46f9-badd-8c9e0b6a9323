import { useState, useEffect } from "react"
import { useParams, useNavigate } from "react-router-dom"
import axios from "axios"

const AddProfile = () => {
  const { id, role, profileId } = useParams()
  const navigate = useNavigate()
  const [currentStep, setCurrentStep] = useState(1)
  const [toast, setToast] = useState({ message: "", type: "", visible: false })
  const [profilePhoto, setProfilePhoto] = useState(null)
  const [isEditing, setIsEditing] = useState(false)
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    full_name: "",
    designation: "",
    department: "",
    company_name: "",
    office_location: "",
    instagram: "",
    facebook: "",
    work_email: "",
    office_number: "",
    linkedin_url: "",

    personal_email: "",
    mobile_number: "",
    date_of_birth: "",
    marital_status: "",
    children: "",
    hobbies: [],
    clubs: [],
    education: [],
    personality_traits: "",

    career_history: "",
    key_responsibilities: "",
    kpis: "",

    decision_role: "",
    reports_to: "",
    influenced_by: "",
    has_influence_over: "",
    authority_level: [],
  })

  useEffect(() => {
    if (profileId) {
      setIsEditing(true)
      setLoading(true)
      fetchProfileData()
    }
  }, [profileId])

  const fetchProfileData = async () => {
    try {
      const response = await axios.get(`http://localhost:5000/api/profiles/${profileId}`)
      const profile = response.data
      
      const authorityArray = profile.authority_level ? profile.authority_level.split(",") : []
      const hobbiesArray = profile.hobbies ? profile.hobbies.split(",").map(item => item.trim()) : []
      const clubsArray = profile.clubs ? profile.clubs.split(",").map(item => item.trim()) : []
      const educationArray = profile.education ? profile.education.split(",").map(item => item.trim()) : []
      
      setFormData({
        full_name: profile.full_name || "",
        designation: profile.designation || "",
        department: profile.department || "",
        company_name: profile.company_name || "",
        office_location: profile.office_location || "",
        instagram: profile.instagram || "",
        facebook: profile.facebook || "",
        work_email: profile.work_email || "",
        office_number: profile.office_number || "",
        linkedin_url: profile.linkedin_url || "",
        personal_email: profile.personal_email || "",
        mobile_number: profile.mobile_number || "",
        date_of_birth: profile.date_of_birth ? profile.date_of_birth.split('T')[0] : "",
        marital_status: profile.marital_status || "",
        children: profile.children || "",
        hobbies: hobbiesArray,
        clubs: clubsArray,
        education: educationArray,
        personality_traits: profile.personality_traits || "",
        career_history: profile.career_history || "",
        key_responsibilities: profile.key_responsibilities || "",
        kpis: profile.kpis || "",
        decision_role: profile.decision_role || "",
        reports_to: profile.reports_to || "",
        influenced_by: profile.influenced_by || "",
        has_influence_over: profile.has_influence_over || "",
        authority_level: authorityArray,
      })
      
      showToast("Profile loaded for editing", "success")
    } catch (error) {
      console.error("Error fetching profile:", error)
      showToast("Error loading profile data", "error")
    } finally {
      setLoading(false)
    }
  }

  const showToast = (message, type) => {
    setToast({ message, type, visible: true })
    setTimeout(() => setToast({ message: "", type: "", visible: false }), 3000)
  }

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target

    if (type === "checkbox") {
      if (checked) {
        setFormData({
          ...formData,
          authority_level: [...formData.authority_level, value],
        })
      } else {
        setFormData({
          ...formData,
          authority_level: formData.authority_level.filter((item) => item !== value),
        })
      }
    } else if (name === "marital_status") {
      setFormData({
        ...formData,
        marital_status: value,
      })
    } else if (name === "decision_role") {
      setFormData({
        ...formData,
        decision_role: value,
      })
    } else {
      setFormData({
        ...formData,
        [name]: value,
      })
    }
  }

  const addArrayItem = (fieldName, item) => {
    if (item.trim() && !formData[fieldName].includes(item.trim())) {
      setFormData({
        ...formData,
        [fieldName]: [...formData[fieldName], item.trim()]
      })
    }
  }

  const removeArrayItem = (fieldName, index) => {
    setFormData({
      ...formData,
      [fieldName]: formData[fieldName].filter((_, i) => i !== index)
    })
  }

  const handleFileChange = (e) => {
    setProfilePhoto(e.target.files[0])
  }

  const nextStep = () => {
    setCurrentStep(currentStep + 1)
    window.scrollTo(0, 0)
  }

  const prevStep = () => {
    setCurrentStep(currentStep - 1)
    window.scrollTo(0, 0)
  }

  const handleSubmit = async (e) => {
    e.preventDefault()

    try {
      const data = new FormData()
      
      if (!isEditing) {
        data.append("account_id", id)
      }

      for (const key in formData) {
        if (key === "authority_level" && formData[key].length > 0) {
          formData[key].forEach((value) => {
            data.append("authority_level", value)
          })
        } else if (key === "hobbies" && formData[key].length > 0) {
          data.append("hobbies", formData[key].join(", "))
        } else if (key === "clubs" && formData[key].length > 0) {
          data.append("clubs", formData[key].join(", "))
        } else if (key === "education" && formData[key].length > 0) {
          data.append("education", formData[key].join(", "))
        } else if (typeof formData[key] === 'string') {
          data.append(key, formData[key])
        }
      }

      if (profilePhoto) {
        data.append("profile_photo", profilePhoto)
      }

      let response
      if (isEditing) {
        response = await axios.put(`http://localhost:5000/api/profiles/update/${profileId}`, data)
        console.log("Profile updated:", response.data)
        showToast("Profile updated successfully!", "success")
      } else {
        response = await axios.post("http://localhost:5000/api/profiles/add", data)
        console.log("Profile created:", response.data)
        showToast("Profile created successfully!", "success")
      }

      setTimeout(() => {
        if (isEditing) {
          navigate(`/admin/${role}/account/${id}/profile/${profileId}`)
        } else {
          navigate(`/admin/${role}/account/${id}`)
        }
      }, 1500)
    } catch (error) {
      console.error(`Error ${isEditing ? 'updating' : 'creating'} profile:`, error)
      showToast(`Error ${isEditing ? 'updating' : 'creating'} profile: ${error.response?.data?.error || error.message}`, "error")
    }
  }

  const goBack = () => {
    if (isEditing) {
      navigate(`/admin/${role}/account/${id}/profile/${profileId}`)
    } else {
      navigate(`/admin/${role}/account/${id}`)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-purple-900 flex items-center justify-center relative overflow-hidden">
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-20 left-20 w-64 h-64 bg-blue-200/20 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-32 right-16 w-80 h-80 bg-indigo-200/15 rounded-full blur-3xl animate-pulse delay-1000"></div>
        </div>
        
        <div className="bg-white rounded-2xl shadow-xl p-8 relative z-10">
          <div className="flex flex-col items-center space-y-3">
            <div className="w-12 h-12 border-4 border-blue-500/30 border-t-blue-600 rounded-full animate-spin"></div>
            <div className="text-xl font-bold text-gray-800">
              Loading Profile Data...
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div 
      className="min-h-screen p-4 md:p-8 relative overflow-hidden"
      style={{
        backgroundImage: 'url(https://img.freepik.com/free-vector/gradient-abstract-wireframe-background_23-2149009903.jpg?semt=ais_hybrid&w=740)',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }}
    >
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 left-20 w-72 h-72 bg-blue-200/30 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-32 right-16 w-96 h-96 bg-indigo-200/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-purple-200/20 rounded-full blur-2xl animate-pulse delay-500"></div>
      </div>

      <div className="max-w-4xl mx-auto bg-white rounded-3xl shadow-2xl shadow-blue-200/50 border border-white/20 p-8 md:p-12 relative z-10 overflow-hidden">
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-400/10 to-indigo-400/10 rounded-full blur-2xl"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-br from-purple-400/10 to-pink-400/10 rounded-full blur-xl"></div>
        <button 
          onClick={goBack} 
          className="mb-8 group flex items-center text-gray-600 hover:text-blue-600 transition-all duration-300 bg-white/50 hover:bg-white/80 backdrop-blur-sm rounded-xl px-4 py-3 border border-gray-200/50 hover:border-blue-300/50 hover:shadow-lg hover:shadow-blue-200/30"
        >
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            className="h-5 w-5 mr-2 group-hover:-translate-x-1 transition-transform duration-300" 
            viewBox="0 0 20 20" 
            fill="currentColor"
          >
            <path
              fillRule="evenodd"
              d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z"
              clipRule="evenodd"
            />
          </svg>
          <span className="font-medium">{isEditing ? "Back to Profile" : "Back to Account"}</span>
        </button>

        <div className="text-center mb-10">
          <h2 className="text-2xl font-bold bg-gradient-to-r from-gray-800 via-blue-800 to-indigo-800 bg-clip-text text-transparent mb-4">
            {isEditing ? "Edit Client Profile" : "Add New Client Profile"}
          </h2>
          <p className="text-gray-600 text-sm">
            {isEditing ? "Update the profile information" : "Complete the comprehensive profile information"}
          </p>
        </div>

        <div className="flex justify-between mb-12 bg-gray-50/80 backdrop-blur-sm rounded-2xl p-6 border border-gray-200/50">
          <div
            className={`flex flex-col items-center ${
              currentStep >= 1 ? "text-blue-600" : "text-gray-400"
            } transition-all duration-300`}
          >
            <div
              className={`w-12 h-12 rounded-full flex items-center justify-center mb-2 font-semibold text-lg transition-all duration-300 ${
                currentStep >= 1 
                  ? "bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg shadow-blue-200/50" 
                  : "bg-gray-200 text-gray-500"
              }`}
            >
              1
            </div>
            <span className="text-xs font-medium">Basic Info</span>
          </div>
          <div className="flex-1 h-1 self-center bg-gray-200 mx-4 rounded-full overflow-hidden">
            <div 
              className={`h-full transition-all duration-500 ${
                currentStep >= 2 ? "bg-gradient-to-r from-blue-500 to-blue-600" : "bg-gray-200"
              }`}
              style={{ width: currentStep >= 2 ? "100%" : "0%" }}
            ></div>
          </div>
          <div
            className={`flex flex-col items-center ${
              currentStep >= 2 ? "text-blue-600" : "text-gray-400"
            } transition-all duration-300`}
          >
            <div
              className={`w-12 h-12 rounded-full flex items-center justify-center mb-2 font-semibold text-lg transition-all duration-300 ${
                currentStep >= 2 
                  ? "bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg shadow-blue-200/50" 
                  : "bg-gray-200 text-gray-500"
              }`}
            >
              2
            </div>
            <span className="text-xs font-medium">Personal Info</span>
          </div>
          <div className="flex-1 h-1 self-center bg-gray-200 mx-4 rounded-full overflow-hidden">
            <div 
              className={`h-full transition-all duration-500 ${
                currentStep >= 3 ? "bg-gradient-to-r from-blue-500 to-blue-600" : "bg-gray-200"
              }`}
              style={{ width: currentStep >= 3 ? "100%" : "0%" }}
            ></div>
          </div>
          <div
            className={`flex flex-col items-center ${
              currentStep >= 3 ? "text-blue-600" : "text-gray-400"
            } transition-all duration-300`}
          >
            <div
              className={`w-12 h-12 rounded-full flex items-center justify-center mb-2 font-semibold text-lg transition-all duration-300 ${
                currentStep >= 3 
                  ? "bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg shadow-blue-200/50" 
                  : "bg-gray-200 text-gray-500"
              }`}
            >
              3
            </div>
            <span className="text-xs font-medium">Professional</span>
          </div>
        </div>

        <form onSubmit={handleSubmit}>
          {currentStep === 1 && (
            <div className="space-y-6 bg-white rounded-2xl p-8 border border-gray-200/50 shadow-xl shadow-blue-100/20">
              <div className="flex items-center mb-6">
                <div className="w-2 h-8 bg-gradient-to-b from-blue-500 to-blue-600 rounded-full mr-4"></div>
                <h3 className="text-xl font-bold bg-gradient-to-r from-gray-800 to-blue-800 bg-clip-text text-transparent">Basic Information</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="md:col-span-2">
                  <label className="text-sm font-semibold text-gray-700 mb-2 flex items-center">
                    <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                    Full Name *
                  </label>
                  <input
                    type="text"
                    name="full_name"
                    value={formData.full_name}
                    onChange={handleChange}
                    className="w-full border-0 bg-white/80 backdrop-blur-sm rounded-xl p-4 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500/50 shadow-lg hover:shadow-xl transition-all duration-300 placeholder-gray-400"
                    required
                  />
                </div>

                <div>
                  <label className="text-sm font-semibold text-gray-700 mb-2 flex items-center">
                    <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                    Designation / Job Title
                  </label>
                  <input
                    type="text"
                    name="designation"
                    value={formData.designation}
                    onChange={handleChange}
                    className="w-full border-0 bg-white/80 backdrop-blur-sm rounded-xl p-4 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500/50 shadow-lg hover:shadow-xl transition-all duration-300 placeholder-gray-400"
                  />
                </div>

                <div>
                  <label className="text-sm font-semibold text-gray-700 mb-2 flex items-center">
                    <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                    Department
                  </label>
                  <input
                    type="text"
                    name="department"
                    value={formData.department}
                    onChange={handleChange}
                    className="w-full border-0 bg-white/80 backdrop-blur-sm rounded-xl p-4 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500/50 shadow-lg hover:shadow-xl transition-all duration-300 placeholder-gray-400"
                  />
                </div>

                <div>
                  <label className="text-sm font-semibold text-gray-700 mb-2 flex items-center">
                    <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                    Company Name
                  </label>
                  <input
                    type="text"
                    name="company_name"
                    value={formData.company_name}
                    onChange={handleChange}
                    className="w-full border-0 bg-white/80 backdrop-blur-sm rounded-xl p-4 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500/50 shadow-lg hover:shadow-xl transition-all duration-300 placeholder-gray-400"
                  />
                </div>

                <div>
                  <label className="text-sm font-semibold text-gray-700 mb-2 flex items-center">
                    <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                    Office Location / Region
                  </label>
                  <input
                    type="text"
                    name="office_location"
                    value={formData.office_location}
                    onChange={handleChange}
                    className="w-full border-0 bg-white/80 backdrop-blur-sm rounded-xl p-4 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500/50 shadow-lg hover:shadow-xl transition-all duration-300 placeholder-gray-400"
                  />
                </div>

                <div>
                  <label className="text-sm font-semibold text-gray-700 mb-2 flex items-center">
                    <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                    Work Email
                  </label>
                  <input
                    type="email"
                    name="work_email"
                    value={formData.work_email}
                    onChange={handleChange}
                    className="w-full border-0 bg-white/80 backdrop-blur-sm rounded-xl p-4 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500/50 shadow-lg hover:shadow-xl transition-all duration-300 placeholder-gray-400"
                  />
                </div>

                <div>
                  <label className="text-sm font-semibold text-gray-700 mb-2 flex items-center">
                    <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                    Office Number
                  </label>
                  <input
                    type="text"
                    name="office_number"
                    value={formData.office_number}
                    onChange={handleChange}
                    className="w-full border-0 bg-white/80 backdrop-blur-sm rounded-xl p-4 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500/50 shadow-lg hover:shadow-xl transition-all duration-300 placeholder-gray-400"
                  />
                </div>

                <div>
                  <label className="text-sm font-semibold text-gray-700 mb-2 flex items-center">
                    <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                    LinkedIn Profile URL
                  </label>
                  <input
                    type="text"
                    name="linkedin_url"
                    value={formData.linkedin_url}
                    onChange={handleChange}
                    className="w-full border-0 bg-white/80 backdrop-blur-sm rounded-xl p-4 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500/50 shadow-lg hover:shadow-xl transition-all duration-300 placeholder-gray-400"
                  />
                </div>

                <div>
                  <label className="text-sm font-semibold text-gray-700 mb-2 flex items-center">
                    <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                    Instagram
                  </label>
                  <input
                    type="text"
                    name="instagram"
                    value={formData.instagram}
                    onChange={handleChange}
                    className="w-full border-0 bg-white/80 backdrop-blur-sm rounded-xl p-4 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500/50 shadow-lg hover:shadow-xl transition-all duration-300 placeholder-gray-400"
                  />
                </div>

                <div>
                  <label className="text-sm font-semibold text-gray-700 mb-2 flex items-center">
                    <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                    Facebook
                  </label>
                  <input
                    type="text"
                    name="facebook"
                    value={formData.facebook}
                    onChange={handleChange}
                    className="w-full border-0 bg-white/80 backdrop-blur-sm rounded-xl p-4 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500/50 shadow-lg hover:shadow-xl transition-all duration-300 placeholder-gray-400"
                  />
                </div>
              </div>

              <div className="flex justify-end mt-8">
                <button
                  type="button"
                  onClick={nextStep}
                  className="group bg-gradient-to-r from-blue-600 to-blue-700 text-white px-8 py-4 rounded-xl hover:from-blue-700 hover:to-blue-800 transition-all duration-300 shadow-lg hover:shadow-xl hover:shadow-blue-200/50 flex items-center font-semibold"
                >
                  <span>Next Step</span>
                  <svg 
                    className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-300" 
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </button>
              </div>
            </div>
          )}

          {currentStep === 2 && (
            <div className="space-y-6 bg-white rounded-2xl p-8 border border-gray-200/50 shadow-xl shadow-indigo-100/20">
              <div className="flex items-center mb-6">
                <div className="w-2 h-8 bg-gradient-to-b from-indigo-500 to-purple-600 rounded-full mr-4"></div>
                <h3 className="text-2xl font-bold bg-gradient-to-r from-gray-800 to-indigo-800 bg-clip-text text-transparent">Personal Information</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="text-sm font-semibold text-gray-700 mb-2 flex items-center">
                    <span className="w-2 h-2 bg-indigo-500 rounded-full mr-2"></span>
                    Personal Email
                  </label>
                  <input
                    type="email"
                    name="personal_email"
                    value={formData.personal_email}
                    onChange={handleChange}
                    className="w-full border-0 bg-white/80 backdrop-blur-sm rounded-xl p-4 text-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500/50 shadow-lg hover:shadow-xl transition-all duration-300 placeholder-gray-400"
                  />
                </div>

                <div>
                  <label className="text-sm font-semibold text-gray-700 mb-2 flex items-center">
                    <span className="w-2 h-2 bg-indigo-500 rounded-full mr-2"></span>
                    Mobile Number
                  </label>
                  <input
                    type="text"
                    name="mobile_number"
                    value={formData.mobile_number}
                    onChange={handleChange}
                    className="w-full border-0 bg-white/80 backdrop-blur-sm rounded-xl p-4 text-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500/50 shadow-lg hover:shadow-xl transition-all duration-300 placeholder-gray-400"
                  />
                </div>

                <div>
                  <label className="text-sm font-semibold text-gray-700 mb-2 flex items-center">
                    <span className="w-2 h-2 bg-indigo-500 rounded-full mr-2"></span>
                    Date of Birth
                  </label>
                  <input
                    type="date"
                    name="date_of_birth"
                    value={formData.date_of_birth}
                    onChange={handleChange}
                    className="w-full border-0 bg-white/80 backdrop-blur-sm rounded-xl p-4 text-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500/50 shadow-lg hover:shadow-xl transition-all duration-300"
                  />
                </div>

                <div>
                  <label className="text-sm font-semibold text-gray-700 mb-2 flex items-center">
                    <span className="w-2 h-2 bg-indigo-500 rounded-full mr-2"></span>
                    Marital Status
                  </label>
                  <div className="flex flex-wrap gap-4 mt-1 p-4 bg-white/60 backdrop-blur-sm rounded-xl border border-gray-200/50">
                    <label className="inline-flex items-center cursor-pointer hover:bg-white/80 rounded-lg p-2 transition-all duration-300">
                      <input
                        type="radio"
                        name="marital_status"
                        value="Single"
                        checked={formData.marital_status === "Single"}
                        onChange={handleChange}
                        className="form-radio h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                      />
                      <span className="ml-2 text-gray-700 font-medium">Single</span>
                    </label>
                    <label className="inline-flex items-center cursor-pointer hover:bg-white/80 rounded-lg p-2 transition-all duration-300">
                      <input
                        type="radio"
                        name="marital_status"
                        value="Married"
                        checked={formData.marital_status === "Married"}
                        onChange={handleChange}
                        className="form-radio h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                      />
                      <span className="ml-2 text-gray-700 font-medium">Married</span>
                    </label>
                    <label className="inline-flex items-center cursor-pointer hover:bg-white/80 rounded-lg p-2 transition-all duration-300">
                      <input
                        type="radio"
                        name="marital_status"
                        value="Divorced"
                        checked={formData.marital_status === "Divorced"}
                        onChange={handleChange}
                        className="form-radio h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                      />
                      <span className="ml-2 text-gray-700 font-medium">Divorced</span>
                    </label>
                  </div>
                </div>

                <div>
                  <label className="text-sm font-semibold text-gray-700 mb-2 flex items-center">
                    <span className="w-2 h-2 bg-indigo-500 rounded-full mr-2"></span>
                    Children
                  </label>
                  <input
                    type="text"
                    name="children"
                    value={formData.children}
                    onChange={handleChange}
                    className="w-full border-0 bg-white/80 backdrop-blur-sm rounded-xl p-4 text-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500/50 shadow-lg hover:shadow-xl transition-all duration-300 placeholder-gray-400"
                  />
                </div>

                <div>
                  <label className="text-sm font-semibold text-gray-700 mb-2 flex items-center">
                    <span className="w-2 h-2 bg-indigo-500 rounded-full mr-2"></span>
                    Hobbies / Interests
                  </label>
                  <div className="space-y-3">
                    <div className="flex gap-2">
                      <input
                        type="text"
                        className="flex-1 border-0 bg-white/80 backdrop-blur-sm rounded-xl p-4 text-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500/50 shadow-lg hover:shadow-xl transition-all duration-300 placeholder-gray-400"
                        onKeyPress={(e) => {
                          if (e.key === 'Enter') {
                            e.preventDefault()
                            addArrayItem('hobbies', e.target.value)
                            e.target.value = ''
                          }
                        }}
                      />
                      <button
                        type="button"
                        onClick={(e) => {
                          const input = e.target.parentElement.querySelector('input')
                          addArrayItem('hobbies', input.value)
                          input.value = ''
                        }}
                        className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-xl hover:from-indigo-600 hover:to-purple-700 transition-all duration-300 shadow-lg flex items-center justify-center"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                      </button>
                    </div>
                    {formData.hobbies.length > 0 && (
                      <div className="flex flex-wrap gap-2">
                        {formData.hobbies.map((hobby, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-3 py-1 bg-gradient-to-r from-indigo-100 to-purple-100 text-indigo-800 rounded-full text-sm font-medium"
                          >
                            {hobby}
                            <button
                              type="button"
                              onClick={() => removeArrayItem('hobbies', index)}
                              className="ml-2 text-indigo-600 hover:text-red-600 transition-colors duration-200"
                            >
                              ×
                            </button>
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                </div>

                <div>
                  <label className="text-sm font-semibold text-gray-700 mb-2 flex items-center">
                    <span className="w-2 h-2 bg-indigo-500 rounded-full mr-2"></span>
                    Clubs / Associations
                  </label>
                  <div className="space-y-3">
                    <div className="flex gap-2">
                      <input
                        type="text"
                        className="flex-1 border-0 bg-white/80 backdrop-blur-sm rounded-xl p-4 text-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500/50 shadow-lg hover:shadow-xl transition-all duration-300 placeholder-gray-400"
                        onKeyPress={(e) => {
                          if (e.key === 'Enter') {
                            e.preventDefault()
                            addArrayItem('clubs', e.target.value)
                            e.target.value = ''
                          }
                        }}
                      />
                      <button
                        type="button"
                        onClick={(e) => {
                          const input = e.target.parentElement.querySelector('input')
                          addArrayItem('clubs', input.value)
                          input.value = ''
                        }}
                        className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-xl hover:from-indigo-600 hover:to-purple-700 transition-all duration-300 shadow-lg flex items-center justify-center"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                      </button>
                    </div>
                    {formData.clubs.length > 0 && (
                      <div className="flex flex-wrap gap-2">
                        {formData.clubs.map((club, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-3 py-1 bg-gradient-to-r from-indigo-100 to-purple-100 text-indigo-800 rounded-full text-sm font-medium"
                          >
                            {club}
                            <button
                              type="button"
                              onClick={() => removeArrayItem('clubs', index)}
                              className="ml-2 text-indigo-600 hover:text-red-600 transition-colors duration-200"
                            >
                              ×
                            </button>
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                </div>

                <div>
                  <label className="text-sm font-semibold text-gray-700 mb-2 flex items-center">
                    <span className="w-2 h-2 bg-indigo-500 rounded-full mr-2"></span>
                    Education / Background
                  </label>
                  <div className="space-y-3">
                    <div className="flex gap-2">
                      <input
                        type="text"
                        className="flex-1 border-0 bg-white/80 backdrop-blur-sm rounded-xl p-4 text-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500/50 shadow-lg hover:shadow-xl transition-all duration-300 placeholder-gray-400"
                        onKeyPress={(e) => {
                          if (e.key === 'Enter') {
                            e.preventDefault()
                            addArrayItem('education', e.target.value)
                            e.target.value = ''
                          }
                        }}
                      />
                      <button
                        type="button"
                        onClick={(e) => {
                          const input = e.target.parentElement.querySelector('input')
                          addArrayItem('education', input.value)
                          input.value = ''
                        }}
                        className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-xl hover:from-indigo-600 hover:to-purple-700 transition-all duration-300 shadow-lg flex items-center justify-center"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                      </button>
                    </div>
                    {formData.education.length > 0 && (
                      <div className="flex flex-wrap gap-2">
                        {formData.education.map((edu, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-3 py-1 bg-gradient-to-r from-indigo-100 to-purple-100 text-indigo-800 rounded-full text-sm font-medium"
                          >
                            {edu}
                            <button
                              type="button"
                              onClick={() => removeArrayItem('education', index)}
                              className="ml-2 text-indigo-600 hover:text-red-600 transition-colors duration-200"
                            >
                              ×
                            </button>
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                </div>

                <div className="md:col-span-2">
                  <label className="text-sm font-semibold text-gray-700 mb-2 flex items-center">
                    <span className="w-2 h-2 bg-indigo-500 rounded-full mr-2"></span>
                    Personality Traits
                  </label>
                  <input
                    type="text"
                    name="personality_traits"
                    value={formData.personality_traits}
                    onChange={handleChange}
                    className="w-full border-0 bg-white/80 backdrop-blur-sm rounded-xl p-4 text-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500/50 shadow-lg hover:shadow-xl transition-all duration-300 placeholder-gray-400"
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="text-sm font-semibold text-gray-700 mb-2 flex items-center">
                    <span className="w-2 h-2 bg-indigo-500 rounded-full mr-2"></span>
                    Profile Photo
                  </label>
                  <div className="relative">
                    <input
                      type="file"
                      onChange={handleFileChange}
                      className="w-full text-gray-700 border-0 bg-white/80 backdrop-blur-sm rounded-xl p-4 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:bg-gradient-to-r file:from-indigo-500 file:to-purple-600 file:text-white file:hover:from-indigo-600 file:hover:to-purple-700 file:transition-all file:duration-300 shadow-lg hover:shadow-xl transition-all duration-300"
                    />
                  </div>
                </div>
              </div>

              <div className="flex justify-between mt-8">
                <button
                  type="button"
                  onClick={prevStep}
                  className="group bg-gradient-to-r from-gray-500 to-gray-600 text-white px-8 py-4 rounded-xl hover:from-gray-600 hover:to-gray-700 transition-all duration-300 shadow-lg hover:shadow-xl flex items-center font-semibold"
                >
                  <svg 
                    className="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform duration-300" 
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                  <span>Previous</span>
                </button>
                <button
                  type="button"
                  onClick={nextStep}
                  className="group bg-gradient-to-r from-indigo-600 to-purple-700 text-white px-8 py-4 rounded-xl hover:from-indigo-700 hover:to-purple-800 transition-all duration-300 shadow-lg hover:shadow-xl hover:shadow-purple-200/50 flex items-center font-semibold"
                >
                  <span>Next Step</span>
                  <svg 
                    className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-300" 
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </button>
              </div>
            </div>
          )}

          {currentStep === 3 && (
            <div className="space-y-6 bg-white rounded-2xl p-8 border border-gray-200/50 shadow-xl shadow-purple-100/20">
              <div className="flex items-center mb-6">
                <div className="w-2 h-8 bg-gradient-to-b from-purple-500 to-pink-600 rounded-full mr-4"></div>
                <h3 className="text-2xl font-bold bg-gradient-to-r from-gray-800 to-purple-800 bg-clip-text text-transparent">Professional Background</h3>
              </div>

              <div className="space-y-6">
                <div>
                  <label className="text-sm font-semibold text-gray-700 mb-2 flex items-center">
                    <span className="w-2 h-2 bg-purple-500 rounded-full mr-2"></span>
                    Career History
                  </label>
                  <textarea
                    name="career_history"
                    value={formData.career_history}
                    onChange={handleChange}
                    className="w-full border-0 bg-white/80 backdrop-blur-sm rounded-xl p-4 text-gray-700 focus:outline-none focus:ring-2 focus:ring-purple-500/50 shadow-lg hover:shadow-xl transition-all duration-300 resize-y placeholder-gray-400"
                    rows="4"
                  ></textarea>
                </div>

                <div>
                  <label className="text-sm font-semibold text-gray-700 mb-2 flex items-center">
                    <span className="w-2 h-2 bg-purple-500 rounded-full mr-2"></span>
                    Key Responsibilities
                  </label>
                  <textarea
                    name="key_responsibilities"
                    value={formData.key_responsibilities}
                    onChange={handleChange}
                    className="w-full border-0 bg-white/80 backdrop-blur-sm rounded-xl p-4 text-gray-700 focus:outline-none focus:ring-2 focus:ring-purple-500/50 shadow-lg hover:shadow-xl transition-all duration-300 resize-y placeholder-gray-400"
                    rows="4"
                  ></textarea>
                </div>

                <div>
                  <label className="text-sm font-semibold text-gray-700 mb-2 flex items-center">
                    <span className="w-2 h-2 bg-purple-500 rounded-full mr-2"></span>
                    KPIs / Performance Goals
                  </label>
                  <textarea
                    name="kpis"
                    value={formData.kpis}
                    onChange={handleChange}
                    className="w-full border-0 bg-white/80 backdrop-blur-sm rounded-xl p-4 text-gray-700 focus:outline-none focus:ring-2 focus:ring-purple-500/50 shadow-lg hover:shadow-xl transition-all duration-300 resize-y placeholder-gray-400"
                    rows="4"
                  ></textarea>
                </div>
              </div>

              <div className="bg-gradient-to-br from-pink-50/50 to-purple-50/50 backdrop-blur-sm rounded-2xl p-6 border border-pink-200/30 mt-8">
                <div className="flex items-center mb-6">
                  <div className="w-2 h-6 bg-gradient-to-b from-pink-500 to-purple-600 rounded-full mr-4"></div>
                  <h4 className="text-xl font-bold bg-gradient-to-r from-gray-800 to-pink-800 bg-clip-text text-transparent">Decision Process Role</h4>
                </div>

                <div className="space-y-6">
                  <div>
                    <label className="text-sm font-semibold text-gray-700 mb-3 flex items-center">
                      <span className="w-2 h-2 bg-pink-500 rounded-full mr-2"></span>
                      Decision Role
                    </label>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                      <label className="relative cursor-pointer group">
                        <input
                          type="radio"
                          name="decision_role"
                          value="Decision Maker"
                          checked={formData.decision_role === "Decision Maker"}
                          onChange={handleChange}
                          className="sr-only"
                        />
                        <div className={`p-4 rounded-xl border-2 transition-all duration-300 text-center ${
                          formData.decision_role === "Decision Maker"
                            ? "border-pink-500 bg-gradient-to-br from-pink-500 to-purple-600 text-white shadow-lg"
                            : "border-gray-200 bg-white/80 text-gray-700 hover:border-pink-300 hover:bg-pink-50/50"
                        }`}>
                          <span className="font-medium text-sm">Decision Maker</span>
                        </div>
                      </label>
                      <label className="relative cursor-pointer group">
                        <input
                          type="radio"
                          name="decision_role"
                          value="Influencer"
                          checked={formData.decision_role === "Influencer"}
                          onChange={handleChange}
                          className="sr-only"
                        />
                        <div className={`p-4 rounded-xl border-2 transition-all duration-300 text-center ${
                          formData.decision_role === "Influencer"
                            ? "border-pink-500 bg-gradient-to-br from-pink-500 to-purple-600 text-white shadow-lg"
                            : "border-gray-200 bg-white/80 text-gray-700 hover:border-pink-300 hover:bg-pink-50/50"
                        }`}>
                          <span className="font-medium text-sm">Influencer</span>
                        </div>
                      </label>
                      <label className="relative cursor-pointer group">
                        <input
                          type="radio"
                          name="decision_role"
                          value="User"
                          checked={formData.decision_role === "User"}
                          onChange={handleChange}
                          className="sr-only"
                        />
                        <div className={`p-4 rounded-xl border-2 transition-all duration-300 text-center ${
                          formData.decision_role === "User"
                            ? "border-pink-500 bg-gradient-to-br from-pink-500 to-purple-600 text-white shadow-lg"
                            : "border-gray-200 bg-white/80 text-gray-700 hover:border-pink-300 hover:bg-pink-50/50"
                        }`}>
                          <span className="font-medium text-sm">User</span>
                        </div>
                      </label>
                      <label className="relative cursor-pointer group">
                        <input
                          type="radio"
                          name="decision_role"
                          value="Gatekeeper"
                          checked={formData.decision_role === "Gatekeeper"}
                          onChange={handleChange}
                          className="sr-only"
                        />
                        <div className={`p-4 rounded-xl border-2 transition-all duration-300 text-center ${
                          formData.decision_role === "Gatekeeper"
                            ? "border-pink-500 bg-gradient-to-br from-pink-500 to-purple-600 text-white shadow-lg"
                            : "border-gray-200 bg-white/80 text-gray-700 hover:border-pink-300 hover:bg-pink-50/50"
                        }`}>
                          <span className="font-medium text-sm">Gatekeeper</span>
                        </div>
                      </label>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                      <label className="text-sm font-semibold text-gray-700 mb-2 flex items-center">
                        <span className="w-2 h-2 bg-pink-500 rounded-full mr-2"></span>
                        Reports To
                      </label>
                      <input
                        type="text"
                        name="reports_to"
                        value={formData.reports_to}
                        onChange={handleChange}
                        className="w-full border-0 bg-white/80 backdrop-blur-sm rounded-xl p-4 text-gray-700 focus:outline-none focus:ring-2 focus:ring-pink-500/50 shadow-lg hover:shadow-xl transition-all duration-300 placeholder-gray-400"
                      />
                    </div>

                    <div>
                      <label className="text-sm font-semibold text-gray-700 mb-2 flex items-center">
                        <span className="w-2 h-2 bg-pink-500 rounded-full mr-2"></span>
                        Influenced By
                      </label>
                      <input
                        type="text"
                        name="influenced_by"
                        value={formData.influenced_by}
                        onChange={handleChange}
                        className="w-full border-0 bg-white/80 backdrop-blur-sm rounded-xl p-4 text-gray-700 focus:outline-none focus:ring-2 focus:ring-pink-500/50 shadow-lg hover:shadow-xl transition-all duration-300 placeholder-gray-400"
                      />
                    </div>

                    <div>
                      <label className="text-sm font-semibold text-gray-700 mb-2 flex items-center">
                        <span className="w-2 h-2 bg-pink-500 rounded-full mr-2"></span>
                        Has Influence Over
                      </label>
                      <input
                        type="text"
                        name="has_influence_over"
                        value={formData.has_influence_over}
                        onChange={handleChange}
                        className="w-full border-0 bg-white/80 backdrop-blur-sm rounded-xl p-4 text-gray-700 focus:outline-none focus:ring-2 focus:ring-pink-500/50 shadow-lg hover:shadow-xl transition-all duration-300 placeholder-gray-400"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-semibold text-gray-700 mb-3 flex items-center">
                      <span className="w-2 h-2 bg-pink-500 rounded-full mr-2"></span>
                      Level of Authority
                    </label>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                      <label className="relative cursor-pointer group">
                        <input
                          type="checkbox"
                          name="authority_level"
                          value="Strategic"
                          checked={formData.authority_level.includes("Strategic")}
                          onChange={handleChange}
                          className="sr-only"
                        />
                        <div className={`p-4 rounded-xl border-2 transition-all duration-300 text-center ${
                          formData.authority_level.includes("Strategic")
                            ? "border-pink-500 bg-gradient-to-br from-pink-500 to-purple-600 text-white shadow-lg"
                            : "border-gray-200 bg-white/80 text-gray-700 hover:border-pink-300 hover:bg-pink-50/50"
                        }`}>
                          <span className="font-medium text-sm">Strategic</span>
                        </div>
                      </label>
                      <label className="relative cursor-pointer group">
                        <input
                          type="checkbox"
                          name="authority_level"
                          value="Technical"
                          checked={formData.authority_level.includes("Technical")}
                          onChange={handleChange}
                          className="sr-only"
                        />
                        <div className={`p-4 rounded-xl border-2 transition-all duration-300 text-center ${
                          formData.authority_level.includes("Technical")
                            ? "border-pink-500 bg-gradient-to-br from-pink-500 to-purple-600 text-white shadow-lg"
                            : "border-gray-200 bg-white/80 text-gray-700 hover:border-pink-300 hover:bg-pink-50/50"
                        }`}>
                          <span className="font-medium text-sm">Technical</span>
                        </div>
                      </label>
                      <label className="relative cursor-pointer group">
                        <input
                          type="checkbox"
                          name="authority_level"
                          value="Budget"
                          checked={formData.authority_level.includes("Budget")}
                          onChange={handleChange}
                          className="sr-only"
                        />
                        <div className={`p-4 rounded-xl border-2 transition-all duration-300 text-center ${
                          formData.authority_level.includes("Budget")
                            ? "border-pink-500 bg-gradient-to-br from-pink-500 to-purple-600 text-white shadow-lg"
                            : "border-gray-200 bg-white/80 text-gray-700 hover:border-pink-300 hover:bg-pink-50/50"
                        }`}>
                          <span className="font-medium text-sm">Budget</span>
                        </div>
                      </label>
                      <label className="relative cursor-pointer group">
                        <input
                          type="checkbox"
                          name="authority_level"
                          value="Advisory"
                          checked={formData.authority_level.includes("Advisory")}
                          onChange={handleChange}
                          className="sr-only"
                        />
                        <div className={`p-4 rounded-xl border-2 transition-all duration-300 text-center ${
                          formData.authority_level.includes("Advisory")
                            ? "border-pink-500 bg-gradient-to-br from-pink-500 to-purple-600 text-white shadow-lg"
                            : "border-gray-200 bg-white/80 text-gray-700 hover:border-pink-300 hover:bg-pink-50/50"
                        }`}>
                          <span className="font-medium text-sm">Advisory</span>
                        </div>
                      </label>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex justify-between mt-8">
                <button
                  type="button"
                  onClick={prevStep}
                  className="group bg-gradient-to-r from-gray-500 to-gray-600 text-white px-8 py-4 rounded-xl hover:from-gray-600 hover:to-gray-700 transition-all duration-300 shadow-lg hover:shadow-xl flex items-center font-semibold"
                >
                  <svg 
                    className="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform duration-300" 
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                  <span>Previous</span>
                </button>
                <button
                  type="submit"
                  className="group bg-gradient-to-r from-green-600 to-emerald-700 text-white px-8 py-4 rounded-xl hover:from-green-700 hover:to-emerald-800 transition-all duration-300 shadow-lg hover:shadow-xl hover:shadow-green-200/50 flex items-center font-semibold"
                >
                  <span>{isEditing ? "Update Profile" : "Save Profile"}</span>
                  <svg 
                    className="w-5 h-5 ml-2 group-hover:scale-110 transition-transform duration-300" 
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </button>
              </div>
            </div>
          )}
        </form>
      </div>

      {toast.visible && (
        <div className="fixed top-6 right-6 z-50 animate-slide-in-right">
          <div
            className={`px-6 py-4 rounded-2xl shadow-2xl backdrop-blur-xl border text-white transform transition-all duration-500 ${
              toast.type === "success" 
                ? "bg-gradient-to-r from-green-500/90 to-emerald-600/90 border-green-300/30 shadow-green-200/50" 
                : "bg-gradient-to-r from-red-500/90 to-rose-600/90 border-red-300/30 shadow-red-200/50"
            }`}
          >
            <div className="flex items-center space-x-3">
              {toast.type === "success" ? (
                <svg className="w-6 h-6 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              ) : (
                <svg className="w-6 h-6 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              )}
              <span className="font-medium text-sm">{toast.message}</span>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default AddProfile
